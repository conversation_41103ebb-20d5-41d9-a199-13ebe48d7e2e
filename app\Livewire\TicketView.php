<?php

namespace App\Livewire;

use App\Enums\Role;
use App\Events\TicketMessageCreated;
use App\Events\TicketMessageAttachmentsReady;
use App\Events\TicketTypingEvent;
use App\Livewire\Forms\TicketReplyForm;
use App\Jobs\ProcessTicketMediaJob;
use App\Models\Ticket;
use App\Models\TicketAttachment;
use App\Models\TicketMessage;
use App\Models\User;
use App\Services\AITicketService;
use App\Services\DiscordService;
use Illuminate\Support\Facades\Log; // Ensure Log facade is imported
use Illuminate\Support\Facades\Storage;
use Livewire\Attributes\On;
use Livewire\Component;
use Livewire\WithFileUploads;

class TicketView extends Component
{
    use WithFileUploads;

    public Ticket $ticket;
    // Form
    public TicketReplyForm $form;
    public $status;
    public $assignedTo;
    public $isSupporter = false;
    public $quotedMessage = null;
    public $typingUsers = []; // Add property to track typing users
    public $showAiConsentPrompt = false; // Flag to show AI consent prompt
    public $aiResponsePending = false; // Flag to indicate AI is generating a response

    // Upload-related properties
    public $uploadProgress = 0;
    public $isUploading = false;
    public $uploadError = null;
    public $pendingAttachments = [];

    // Livewire 3: Use #[On] attributes instead of $listeners property
    #[On('refresh')]
    public function handleRefresh()
    {
        // $refresh is now handled internally through re-rendering
    }

    #[On('upload:started')]
    public function handleUploadStarted()
    {
        $this->isUploading = true;
        $this->uploadProgress = 0;
        $this->uploadError = null;
    }

    #[On('upload:finished')]
    public function handleUploadFinished()
    {
        $this->isUploading = false;
        $this->uploadProgress = 100;
    }

    #[On('upload:errored')]
    public function handleUploadErrored()
    {
        $this->isUploading = false;

        // Get recent upload error from validation
        $errors = $this->getErrorBag();
        if ($errors->has('form.attachments.0')) { // Check against the form object's property
            $this->uploadError = $errors->first('form.attachments.0');
        } else {
            // Fallback for other error formats
            $this->uploadError = __('tickets.upload_error_occurred');
        }
    }

    #[On('upload:progress')]
    public function handleUploadProgress($progress)
    {
        $this->uploadProgress = $progress;
    }

    #[On('userTyping')]
    public function handleUserTyping()
    {
        // Handle user typing event
    }

    protected function rules()
    {
        return [
            'message' => 'required|string',
            'attachments.*' => 'nullable|file|max:10240', // 10MB max per file
            'status' => 'nullable|in:open,in_progress,closed',
            'assignedTo' => 'nullable|string',
        ];
    }

    public function mount(Ticket $ticket)
    {
        $this->ticket = $ticket;
        $this->status = $ticket->status;
        $this->assignedTo = $ticket->assigned_to;
        $this->isSupporter = auth()->user()->hasRole(Role::MINEWACHE_TEAM);

        // Initialize the form
        $this->form = new TicketReplyForm($this, 'form');

        // Explicitly log WebSocket channel subscription for debugging
        Log::info('Subscribing to ticket channel', [
            'ticket_id' => $this->ticket->id,
            'channel' => 'private:tickets.' . $this->ticket->id
        ]);
    }

    public function render()
    {
        $this->ticket->load(['messages.user', 'user', 'assignedTo']);

        $supporters = [];
        if ($this->isSupporter) {
            $supporters = User::where('permissions', '&', Role::MINEWACHE_TEAM->value)
                ->orderBy('username')
                ->get()
                ->pluck('username', 'id')
                ->toArray();
        }

        // Check if we should show the AI consent prompt
        $this->checkAIConsentPrompt();

        return view('livewire.ticket-view', [
            'supporters' => $supporters,
            'showAiConsentPrompt' => $this->showAiConsentPrompt,
            'aiResponsePending' => $this->aiResponsePending,
        ]);
    }

    /**
     * Check if we should show the AI consent prompt.
     */
    protected function checkAIConsentPrompt()
    {
        // Only show the prompt if:
        // 1. Gemini is enabled
        // 2. The ticket is not closed
        // 3. No supporter is assigned
        // 4. The user hasn't already given or denied consent
        // 5. The user is the ticket creator

        if (!config('services.gemini.enabled', true)) {
            return;
        }

        if ($this->ticket->status === 'closed') {
            return;
        }

        if ($this->ticket->assigned_to) {
            return;
        }

        // If consent has already been explicitly set (true or false), don't show the prompt
        if ($this->ticket->gemini_consent_at !== null) {
            return;
        }

        // Only show the prompt to the ticket creator
        if (auth()->id() !== $this->ticket->user_id) {
            return;
        }

        $this->showAiConsentPrompt = true;
    }

    public function addReply()
    {
        // Check if ticket is closed
        if ($this->ticket->status === 'closed') {
            $this->addError('form.message', __('tickets.ticket_closed_no_replies'));
            return;
        }

        // Check if uploads are still in progress
        if ($this->isUploading) {
            $this->addError('form.message', __('tickets.uploads_in_progress'));
            return;
        }

        // Store the message using the form
        $message = $this->form->store($this->ticket->id);

        // No need to dispatch 'refresh' event - Livewire 3 automatically re-renders
        // when properties change, and the form is already being reset in the store method
    }

    public function updateStatus()
    {
        if (!$this->isSupporter) {
            return;
        }

        $this->validate([
            'status' => 'required|in:open,in_progress,closed',
        ]);

        $this->ticket->status = $this->status;
        $this->ticket->save();

        // Update the status on Discord if integration is enabled
        if (config('services.discord.enabled', false) && $this->ticket->discord_channel_id) {
            $discordService = app(DiscordService::class);
            $discordService->updateTicketStatus($this->ticket);
        }
    }

    public function updateAssignment()
    {
        if (!$this->isSupporter) {
            return;
        }

        $this->validate([
            'assignedTo' => 'nullable|string',
        ]);

        $this->ticket->assigned_to = $this->assignedTo ?: null;
        $this->ticket->save();

        // Update the assignment on Discord if integration is enabled
        if (config('services.discord.enabled', false) && $this->ticket->discord_channel_id) {
            $discordService = app(DiscordService::class);
            $discordService->updateTicketAssignment($this->ticket);
        }
    }

    public function assignToMe()
    {
        if (!$this->isSupporter) {
            return;
        }

        $this->assignedTo = auth()->id();
        $this->updateAssignment();
    }

    /**
     * Set the status and update the ticket in one step.
     *
     * @param string $status
     * @return void
     */
    public function setStatusAndUpdate(string $status)
    {
        if (!$this->isSupporter) {
            return;
        }

        $this->status = $status;
        $this->updateStatus();
    }

    /**
     * Refresh messages when a new message is received via Echo.
     * Optimized to append the new message instead of full refresh.
     */
    #[On('echo-private:tickets.{ticket.id},TicketMessageCreated')]
    public function handleNewMessage($event)
    {
        Log::debug('Livewire received TicketMessageCreated event', ['event' => $event]);

        // Get the message data from the event (assuming it's an array)
        $messageData = $event['message'] ?? null;

        if (!$messageData || !isset($messageData['id'])) {
            Log::warning('Received TicketMessageCreated event with invalid data.', ['event' => $event]);
            return;
        }

        // Check if message already exists in the collection to prevent duplicates
        if ($this->ticket->messages->contains('id', $messageData['id'])) {
            return;
        }

        // Clear typing indicator for the user who just sent a message
        if (isset($messageData['user_id'])) {
            unset($this->typingUsers[$messageData['user_id']]);
        }

        // Play notification sound - dispatch to browser
        $this->dispatch('play-notification');
        Log::debug('Dispatched play-notification event');

        // Create a new TicketMessage model instance from the event data
        // Ensure the event broadcasts the necessary data, including the user relation
        $newMessage = new TicketMessage($messageData);
        // Manually set relations if they are nested arrays in the event
        if (isset($messageData['user'])) {
            $newMessage->setRelation('user', new User($messageData['user']));
        }
        if (isset($messageData['attachments'])) {
            $newMessage->setRelation('attachments', collect($messageData['attachments'])->map(function ($attachmentData) {
                return new TicketAttachment($attachmentData);
            }));
        } else {
             $newMessage->setRelation('attachments', collect()); // Ensure attachments relation is initialized
        }

        // Add the new message to the existing collection
        // Ensure the 'messages' relation was loaded initially in render() or mount()
        if ($this->ticket->relationLoaded('messages')) {
            $this->ticket->messages->push($newMessage);
        } else {
            // Fallback to refresh if messages weren't loaded (should not happen ideally)
            $this->ticket->refresh();
            $this->ticket->load(['messages.user', 'messages.attachments']);
        }

        // Dispatch browser event for frontend JS to update UI immediately
        $this->dispatch('message-added', message: $messageData);
    }

    /**
     * Handle typing event received via Echo.
     */
    #[On('echo-private:tickets.{ticket.id},.typing')] // Match the broadcastAs name, not the class name
    public function handleTypingIndicator($event)
    {
        Log::debug('Livewire received typing event', ['event' => $event]);
        if (isset($event['user']) && $event['user']['id'] !== auth()->id()) {
            $userId = $event['user']['id'];
            $this->typingUsers[$userId] = [
                'data' => $event['user'],
                'timestamp' => now()->timestamp // Use for auto-expiry
            ];

            // Clean up expired typing indicators (e.g., older than 5 seconds)
            $this->cleanupTypingUsers();

            // Dispatch browser event for frontend JS to update UI immediately
            $this->dispatch('typing-update', users: $this->typingUsers);
        }
    }

    /**
     * Remove users who stopped typing more than a few seconds ago.
     */
    private function cleanupTypingUsers()
    {
        $expiryTime = 5; // seconds
        $now = now()->timestamp;
        $this->typingUsers = array_filter($this->typingUsers, function ($user) use ($now, $expiryTime) {
            return ($now - $user['timestamp']) < $expiryTime;
        });
    }

    // The upload event handlers have been moved to use #[On] attributes
    // at the top of the class.

    /**
     * Dismiss upload error message.
     */
    public function dismissUploadError()
    {
        $this->uploadError = null;
    }

    /**
     * Clear all attachments from the form.
     */
    public function clearAttachments()
    {
        $this->form->attachments = [];
    }

    /**
     * Remove a specific attachment from the form.
     *
     * @param int $index The index of the attachment to remove
     * @return void
     */
    public function removeAttachment($index)
    {
        // Make sure the index exists in the attachments array
        if (isset($this->form->attachments[$index])) {
            // Create a new array without the specified attachment
            $attachments = $this->form->attachments;
            unset($attachments[$index]);

            // Re-index the array to ensure sequential keys
            $this->form->attachments = array_values($attachments);
        }
    }

    /**
     * Cancel the current upload.
     * This is called from JavaScript.
     */
    public function cancelUpload()
    {
        // This method is intentionally empty as it's used as a target for wire:click
        // The actual cancellation is handled by the browser when the form is reset
        $this->isUploading = false;
        $this->uploadProgress = 0;
    }

    /**
     * Request user data consent for a specific data type.
     *
     * @param string $dataType The type of data to request consent for
     * @return void
     */
    public function requestDataConsent(string $dataType)
    {
        // Dispatch an event to show the consent modal
        $this->dispatch('requestDataConsent', dataType: $dataType, ticketId: $this->ticket->id);
    }

    /**
     * Handle data sharing consent response.
     *
     * @param array $event The event data
     * @return void
     */
    #[On('dataSharingConsentGranted')]
    #[On('dataSharingConsentDenied')]
    public function handleDataSharingConsent($event)
    {
        $dataType = $event['dataType'] ?? null;
        $ticketId = $event['ticketId'] ?? null;

        // Ensure this is for the current ticket
        if ($ticketId !== $this->ticket->id) {
            return;
        }

        // Refresh the ticket to ensure we have the latest data
        $this->ticket->refresh();

        // If the consent was granted, we can trigger an AI response
        if ($event['name'] === 'dataSharingConsentGranted') {
            // Trigger AI response if needed
            $this->generateAIResponse();
        }
    }

    /**
     * Send message to Discord via DiscordService.
     *
     * @param \App\Models\TicketMessage $message The message to send.
     * @return void
     */
    protected function sendToDiscord(TicketMessage $message)
    {
        if (config('services.discord.enabled', false) && $this->ticket->discord_channel_id) {
            // Ensure message is loaded with necessary relations if needed by DiscordService
            $message->loadMissing(['user', 'attachments', 'ticket']);
            $discordService = app(DiscordService::class);
            $discordService->sendTicketMessage($message);
        }
    }

    /**
     * Handle attachment updates when processing is complete.
     */
    #[On('echo-private:tickets.{ticket.id},TicketMessageAttachmentsReady')]
    public function handleAttachmentsReady($event)
    {
        Log::debug('Livewire received TicketMessageAttachmentsReady event', ['event' => $event]);

        try {
            // Get the message ID and attachments from the event
            $messageId = $event['message_id'] ?? null;
            $attachments = $event['attachments'] ?? [];
            $error = $event['error'] ?? null;

            if ($error) {
                Log::warning('TicketMessageAttachmentsReady event contains error', [
                    'error' => $error,
                    'message_id' => $messageId
                ]);
            }

            if (!$messageId) {
                Log::warning('TicketMessageAttachmentsReady event missing message_id', [
                    'event' => $event
                ]);
                return;
            }

            if (empty($attachments)) {
                Log::warning('TicketMessageAttachmentsReady event has no attachments', [
                    'message_id' => $messageId
                ]);
                // Continue processing even without attachments to update the UI
            }

            // Find the message in the ticket's messages collection
            $message = $this->ticket->messages->firstWhere('id', $messageId);

            if (!$message) {
                Log::info('Message not found in collection, refreshing ticket', [
                    'message_id' => $messageId,
                    'ticket_id' => $this->ticket->id
                ]);

                // If the message isn't in the collection, refresh the ticket
                $this->ticket->refresh();
                $this->ticket->load(['messages.user', 'messages.attachments']);

                // Try to find the message again
                $message = $this->ticket->messages->firstWhere('id', $messageId);

                if (!$message) {
                    Log::warning('Message still not found after refresh', [
                        'message_id' => $messageId,
                        'ticket_id' => $this->ticket->id
                    ]);
                    return;
                }
            }

            // Update the attachments in the message
            foreach ($attachments as $attachmentData) {
                $attachmentId = $attachmentData['id'] ?? null;

                if (!$attachmentId) {
                    Log::warning('Attachment data missing ID', [
                        'attachment_data' => $attachmentData
                    ]);
                    continue;
                }

                $attachment = $message->attachments->firstWhere('id', $attachmentId);

                if ($attachment) {
                    // Update the attachment with the processed data
                    $attachment->fill([
                        'media_url' => $attachmentData['media_url'] ?? null,
                        'download_url' => $attachmentData['download_url'] ?? null,
                        'processing_status' => $attachmentData['processing_status'] ?? 'completed',
                    ]);

                    Log::debug('Updated attachment in message', [
                        'attachment_id' => $attachmentId,
                        'message_id' => $messageId
                    ]);
                } else {
                    Log::warning('Attachment not found in message', [
                        'attachment_id' => $attachmentId,
                        'message_id' => $messageId
                    ]);
                }
            }

            // Dispatch an event to update the UI
            $this->dispatch('attachments-ready', messageId: $messageId, attachments: $attachments);

            Log::info('Dispatched attachments-ready event to UI', [
                'message_id' => $messageId,
                'attachments_count' => count($attachments)
            ]);
        } catch (\Exception $e) {
            Log::error('Error handling TicketMessageAttachmentsReady event', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'event' => $event
            ]);
        }
    }

    /**
     * Listen for real-time status updates via Echo.
     */
    #[On('echo-private:tickets.{ticket.id},TicketStatusUpdated')]
    public function handleStatusUpdate($event)
    {
        Log::debug('Livewire received TicketStatusUpdated event', ['event' => $event]);
        // Update the status property in the component
        $this->status = $event['status'];
        // Also update the ticket model instance if necessary for consistency
        if ($this->ticket) {
            $this->ticket->status = $event['status'];
        }
        // Optionally, you might want to flash a message or trigger a small UI indication
        // session()->flash('info', 'Ticket status updated in real-time.');

        // No need to call $this->dispatch('refresh') or $refresh here,
        // Livewire automatically re-renders when a public property changes.
    }

    /**
     * Listen for AI response generated events via Echo.
     */
    #[On('echo-private:tickets.{ticket.id},TicketAIResponseGenerated')]
    public function handleAIResponseGenerated($event)
    {
        Log::debug('Livewire received TicketAIResponseGenerated event', ['event' => $event]);

        // Reset the AI response pending flag
        $this->aiResponsePending = false;

        // The message should already be handled by the TicketMessageCreated event,
        // but we can add specific handling for AI responses here if needed
    }

    /**
     * Handle Gemini consent given event.
     */
    #[On('gemini-consent-given')]
    public function handleGeminiConsentGiven($ticketId)
    {
        if ($this->ticket->id != $ticketId) {
            return;
        }

        // Refresh the ticket to get the updated consent status
        $this->ticket->refresh();

        // Generate an AI response if consent was given
        if ($this->ticket->gemini_consent) {
            $this->generateAIResponse();
        }
    }

    /**
     * Handle Gemini consent denied event.
     */
    #[On('gemini-consent-denied')]
    public function handleGeminiConsentDenied($ticketId)
    {
        if ($this->ticket->id != $ticketId) {
            return;
        }

        // Refresh the ticket to get the updated consent status
        $this->ticket->refresh();

        // No need to do anything else if consent was denied
    }

    /**
     * Generate an AI response for the ticket.
     */
    public function generateAIResponse()
    {
        // Check if AI assistance is available
        $aiTicketService = app(AITicketService::class);

        if (!$aiTicketService->isAIAssistanceAvailable($this->ticket)) {
            Log::info('AI assistance not available for ticket', [
                'ticket_id' => $this->ticket->id,
            ]);
            return;
        }

        // Überprüfe, ob der Benutzer der Freigabe seines Benutzernamens zugestimmt hat
        $user = $this->ticket->user;
        if (!$user->hasConsentedToShareData('username')) {
            // Fordere Zustimmung für den Benutzernamen an
            $this->requestDataConsent('username');
            return;
        }

        // Set the flag to indicate AI is generating a response
        $this->aiResponsePending = true;

        try {
            // Generate the AI response
            $message = $aiTicketService->generateResponse($this->ticket);

            if ($message) {
                Log::info('AI response generated for ticket', [
                    'ticket_id' => $this->ticket->id,
                    'message_id' => $message->id,
                ]);

                // Send the AI response to Discord if integration is enabled
                if (config('services.discord.enabled', false) && $this->ticket->discord_channel_id) {
                    $this->sendToDiscord($message);
                }
            } else {
                Log::warning('Failed to generate AI response for ticket', [
                    'ticket_id' => $this->ticket->id,
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Error generating AI response', [
                'ticket_id' => $this->ticket->id,
                'error' => $e->getMessage(),
            ]);
        } finally {
            // Reset the flag
            $this->aiResponsePending = false;
        }
    }

    /**
     * Set Gemini consent for the ticket.
     */
    public function setGeminiConsent($consent)
    {
        $this->ticket->gemini_consent = $consent;
        $this->ticket->gemini_consent_at = now();
        $this->ticket->save();

        Log::info('Gemini consent updated for ticket', [
            'ticket_id' => $this->ticket->id,
            'consent' => $consent,
            'user_id' => auth()->id(),
        ]);

        // Hide the consent prompt
        $this->showAiConsentPrompt = false;

        // Wir generieren hier keine AI-Antwort mehr, da der CheckForAIResponse-Listener
        // automatisch eine Antwort generieren wird, wenn ein neues Ticket erstellt wird.
        // Dies verhindert doppelte Nachrichten.
    }

    /**
     * Notify other users that the current user is typing.
     * This method is called when the user types in the message input field.
     */
    public function notifyTyping()
    {
        Log::debug('User is typing in ticket', ['ticket_id' => $this->ticket->id]);

        // Get current user data
        $user = auth()->user();
        if (!$user) {
            return;
        }

        // Prepare user data for the event
        $userData = [
            'id' => $user->id,
            'username' => $user->username,
            'avatar' => $user->avatar
        ];

        // Broadcast the typing event
        event(new TicketTypingEvent($this->ticket->id, $userData));
    }

    /**
     * Set a message to be quoted in the reply.
     *
     * @param int $messageId The ID of the message to quote
     * @return void
     */
    public function quoteMessage($messageId)
    {
        $this->quotedMessage = $this->ticket->messages->firstWhere('id', $messageId);

        // Log the quoted message for debugging
        Log::debug('Message quoted', ['message_id' => $messageId]);
    }

    /**
     * Cancel quoting a message.
     *
     * @return void
     */
    public function cancelQuote()
    {
        $this->quotedMessage = null;

        // Log the cancellation for debugging
        Log::debug('Quote canceled');
    }
}
