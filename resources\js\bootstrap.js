import axios from 'axios';
window.axios = axios;

window.axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';

/**
 * Echo exposes an expressive API for subscribing to channels and listening
 * for events that are broadcast by Laravel. Echo and event broadcasting
 * allows your team to easily build robust real-time web applications.
 */

import Echo from 'laravel-echo';
import Pusher from 'pusher-js';

window.Pusher = Pusher;

// Konfiguration für Reverb
const reverbConfig = {
    broadcaster: 'reverb',
    key: import.meta.env.VITE_REVERB_APP_KEY === '${REVERB_APP_KEY}' ? 'minewache_key' : import.meta.env.VITE_REVERB_APP_KEY || 'minewache_key',
    enabledTransports: ['ws', 'wss'],
    disableStats: true,
    // Explicitly set the CSRF token from the meta tag for Livewire 3 compatibility
    csrfToken: document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'),
    // Add auth endpoint for private channels
    authEndpoint: '/broadcasting/auth',
};

// Determine environment and set appropriate WebSocket configuration
const isLocalDevelopment = window.location.hostname === 'localhost' ||
                            window.location.hostname === '127.0.0.1' ||
                            window.location.hostname.includes('.local') ||
                            import.meta.env.VITE_APP_ENV === 'local' ||
                            import.meta.env.MODE === 'development';
const isHttps = window.location.protocol === 'https:';

// Define Logger if it doesn't exist (fallback for when logger.js hasn't loaded)
if (typeof window.Logger === 'undefined') {
    window.Logger = {
        log: console.log.bind(console),
        debug: console.debug.bind(console),
        warn: console.warn.bind(console),
        error: console.error.bind(console),
        group: console.group.bind(console),
        groupEnd: console.groupEnd.bind(console),
        time: console.time.bind(console),
        timeEnd: console.timeEnd.bind(console),
        forceLog: console.log.bind(console)
    };
}

// Log environment detection
Logger.debug('Environment detection:', {
    hostname: window.location.hostname,
    protocol: window.location.protocol,
    isLocalDevelopment: isLocalDevelopment,
    isHttps: isHttps,
    envHost: import.meta.env.VITE_REVERB_HOST || 'localhost',
    envPort: import.meta.env.VITE_REVERB_PORT || '8080',
    envScheme: import.meta.env.VITE_REVERB_SCHEME || 'http',
    appEnv: import.meta.env.VITE_APP_ENV,
    mode: import.meta.env.MODE
});

// Configure WebSocket based on environment
if (isLocalDevelopment) {
    // Local development configuration
    Logger.log('Using local Reverb configuration');
    reverbConfig.wsHost = 'localhost';  // Force localhost for development

    // Fix for environment variables not being properly interpolated
    const portStr = import.meta.env.VITE_REVERB_PORT;
    const port = portStr === '${REVERB_PORT}' ? 6001 : parseInt(portStr || '6001');

    reverbConfig.wsPort = port;
    reverbConfig.wssPort = port;
    reverbConfig.forceTLS = false;  // Force no TLS for local development

    Logger.debug('WebSocket configuration set to:', {wsHost: reverbConfig.wsHost, wsPort: reverbConfig.wsPort});
} else {
    // Production configuration
    Logger.log('Using production Reverb configuration');

    // Always use the current hostname in production
    reverbConfig.wsHost = window.location.hostname;

    // Set the path to match NGINX proxy configuration
    // NGINX is configured to proxy WebSocket connections through /app to 127.0.0.1:6001
    // The path should be just '/app' - Pusher.js will append the app key automatically
    reverbConfig.wsPath = '/app';

    // Explicitly set the namespace to empty to prevent Pusher.js from adding a duplicate /app path
    reverbConfig.namespace = '';

    Logger.log(`Using WebSocket path: ${reverbConfig.wsPath} with empty namespace to prevent path duplication`);

    // Set appropriate ports and TLS based on protocol
    if (isHttps) {
        reverbConfig.forceTLS = true;
        // In production with HTTPS, we use the default HTTPS port (443)
        // The actual WebSocket server runs on port 6001, but NGINX handles the proxying
        reverbConfig.wsPort = 443;
        reverbConfig.wssPort = 443;
        Logger.log(`Using secure WebSocket (WSS) on port ${reverbConfig.wsPort}`);
    } else {
        reverbConfig.forceTLS = false;
        // In production with HTTP, we use the default HTTP port (80)
        // The actual WebSocket server runs on port 6001, but NGINX handles the proxying
        reverbConfig.wsPort = 80;
        Logger.log(`Using WebSocket (WS) on port ${reverbConfig.wsPort}`);
    }
}

// Initialize Echo with error handling
try {
    window.Echo = new Echo(reverbConfig);

    // Debug-Informationen ausgeben
    Logger.debug('Echo initialized with Reverb config:', {
        broadcaster: reverbConfig.broadcaster,
        key: reverbConfig.key === '${REVERB_APP_KEY}' ? 'Error: Unresolved env variable' : reverbConfig.key,
        wsHost: reverbConfig.wsHost,
        wsPort: reverbConfig.wsPort,
        wssPort: reverbConfig.wssPort,
        wsPath: reverbConfig.wsPath || '/',
        namespace: reverbConfig.namespace,
        forceTLS: reverbConfig.forceTLS,
        enabledTransports: reverbConfig.enabledTransports,
        authEndpoint: reverbConfig.authEndpoint,
        hostname: window.location.hostname,
        protocol: window.location.protocol,
        environment: import.meta.env.MODE || 'unknown'
    });

    // Log success in production as well
    console.log('Laravel Echo initialized successfully with Reverb');
} catch (error) {
    // Always log initialization errors, even in production
    console.error('Failed to initialize Laravel Echo:', error);

    // Provide more detailed error information
    Logger.error('Echo initialization error details:', {
        error: error,
        config: {
            broadcaster: reverbConfig.broadcaster,
            host: reverbConfig.wsHost,
            port: reverbConfig.wsPort,
            path: reverbConfig.wsPath || '/',
            namespace: reverbConfig.namespace,
            forceTLS: reverbConfig.forceTLS
        },
        url: window.location.href
    });
}

// Add connection status event listeners for debugging and error handling
if (window.Echo && window.Echo.connector && window.Echo.connector.pusher) {
    const pusher = window.Echo.connector.pusher;

    pusher.connection.bind('connected', () => {
        Logger.log('WebSocket connected successfully!', {
            socketId: pusher.connection.socket_id,
            connectionId: pusher.connection.connectionId
        });

        // Log success in production as well
        console.log('WebSocket connection established successfully');

        // Dispatch a custom event that can be listened to by other components
        document.dispatchEvent(new CustomEvent('websocket-connected', {
            detail: {
                socketId: pusher.connection.socket_id,
                connectionId: pusher.connection.connectionId
            }
        }));
    });

    pusher.connection.bind('error', (error) => {
        // Always log WebSocket errors, even in production
        console.error('WebSocket connection error:', error);

        // Provide more detailed error information
        Logger.error('WebSocket connection error details:', {
            error: error,
            config: {
                host: reverbConfig.wsHost,
                port: reverbConfig.wsPort,
                path: reverbConfig.wsPath || '/',
                namespace: reverbConfig.namespace,
                forceTLS: reverbConfig.forceTLS
            },
            url: window.location.href
        });

        // Dispatch a custom event that can be listened to by other components
        document.dispatchEvent(new CustomEvent('websocket-error', {
            detail: { error: error }
        }));
    });

    pusher.connection.bind('state_change', (states) => {
        Logger.debug(`WebSocket state changed: ${states.previous} -> ${states.current}`);

        // Dispatch a custom event that can be listened to by other components
        document.dispatchEvent(new CustomEvent('websocket-state-change', {
            detail: { previous: states.previous, current: states.current }
        }));

        // If disconnected, log a warning
        if (states.current === 'disconnected' || states.current === 'failed') {
            console.warn(`WebSocket disconnected. Previous state: ${states.previous}`);
        }
    });

    // Add a reconnection handler
    pusher.connection.bind('connecting', () => {
        Logger.log('Attempting to reconnect to WebSocket...');
    });
}
