/**
 * Ticket View Functions
 *
 * This file contains the JavaScript functions for the ticket view page.
 */

// Define the function
function ticketView() {
    return {
        init() {
            this.scrollToBottom();
            this.setupNotifications();
        },

        handleMessageAdded(message) {
            console.log('Message added:', message);

            // Play notification sound if enabled
            this.playNotificationSound();

            // Get the messages container
            const messagesContainer = document.getElementById('messages-container');
            if (!messagesContainer) return;

            // Check if we're already at the bottom before the new message
            const isAtBottom = messagesContainer.scrollTop >= (messagesContainer.scrollHeight - messagesContainer.clientHeight - 100);

            // Wait for DOM to update with the new message
            setTimeout(() => {
                if (isAtBottom) {
                    // If we were at the bottom, scroll to bottom
                    this.scrollToBottom();
                } else {
                    // If we weren't at the bottom, show the new message indicator
                    this.showNewMessageIndicator();
                }
            }, 100);
        },

        handleAttachmentsReady(messageId, attachments) {
            console.log('Attachments ready for message:', messageId, attachments);

            if (!attachments || !Array.isArray(attachments) || attachments.length === 0) {
                console.warn('No valid attachments received for message:', messageId);
                return;
            }

            // Track if any attachments were updated
            let updatedAny = false;

            attachments.forEach(attachment => {
                if (!attachment || !attachment.id) {
                    console.warn('Invalid attachment data:', attachment);
                    return;
                }

                const attachmentElement = document.querySelector(`[data-attachment-id="${attachment.id}"]`);
                if (!attachmentElement) {
                    console.warn(`Attachment element not found for ID: ${attachment.id}`);
                    return;
                }

                // Find the container to replace (the parent div)
                const containerToReplace = attachmentElement.closest('.attachment-container');
                if (!containerToReplace) {
                    console.warn(`Container not found for attachment ID: ${attachment.id}`);
                    return;
                }

                try {
                    // Create new element based on the attachment type
                    const newElement = this.createAttachmentElement(attachment);

                    // Replace the old container with the new one
                    if (newElement) {
                        containerToReplace.replaceWith(newElement);
                        updatedAny = true;
                        console.log(`Successfully updated attachment ID: ${attachment.id}`);
                    }
                } catch (error) {
                    console.error(`Error updating attachment ID: ${attachment.id}`, error);
                    // Show error state instead of failing silently
                    containerToReplace.innerHTML = `
                        <div class="flex items-center justify-center gap-2 px-3 py-4 text-xs font-medium bg-red-900/30 text-red-300 rounded-xl">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span>Error displaying attachment</span>
                        </div>
                    `;
                }
            });

            // Only re-initialize if we actually updated something
            if (updatedAny) {
                // Re-initialize things if needed (e.g., custom video players, gallery)
                this.initCustomVideoPlayers();
                this.buildGalleryData(); // Rebuild gallery data after updates
                console.log('Re-initialized video players and gallery data');
            }
        },

        handleTypingUpdate(users) {
            // Handle typing indicator updates
            const typingIndicator = document.getElementById('typing-indicator');
            if (!typingIndicator) return;

            // If no users are typing, hide the indicator
            if (!users || Object.keys(users).length === 0) {
                typingIndicator.classList.add('hidden');
                return;
            }

            // Get the first typing user
            const userId = Object.keys(users)[0];
            const userData = users[userId].data;

            // Update the typing indicator with user info
            const userAvatar = typingIndicator.querySelector('img');
            const userName = typingIndicator.querySelector('.text-sm.font-semibold');

            if (userAvatar) {
                userAvatar.src = userData.avatar || '';
                userAvatar.alt = userData.username || 'User';
            }

            if (userName) {
                userName.textContent = userData.username + ' is typing...';
            }

            // Show the typing indicator
            typingIndicator.classList.remove('hidden');

            // Auto-hide after 3 seconds
            setTimeout(() => {
                typingIndicator.classList.add('hidden');
            }, 3000);
        },

        scrollToBottom() {
            const messagesContainer = document.getElementById('messages-container');
            if (!messagesContainer) {
                console.error('Messages container not found');
                return;
            }

            // Force a reflow to ensure the scrollHeight is accurate
            void messagesContainer.offsetHeight;

            // Scroll to the bottom
            messagesContainer.scrollTop = messagesContainer.scrollHeight;

            console.log('Scrolled to bottom, height:', messagesContainer.scrollHeight);

            // Add click handler to the new message indicator
            const newMessageIndicator = document.getElementById('new-message-indicator');
            if (newMessageIndicator) {
                newMessageIndicator.onclick = () => {
                    this.scrollToBottom();
                    newMessageIndicator.classList.add('hidden');
                };
            }
        },

        // --- NEW HELPER FUNCTIONS ---
        createAttachmentElement(attachment) {
            if (attachment.is_image) {
                return this.createImageElement(attachment);
            } else if (attachment.is_video) {
                return this.createVideoElement(attachment);
            } else if (attachment.is_audio) {
                return this.createAudioElement(attachment);
            } else if (attachment.is_pdf) {
                return this.createPdfElement(attachment);
            } else {
                return this.createGenericFileElement(attachment);
            }
        },

        createImageElement(attachment) {
            const div = document.createElement('div');
            div.className = 'attachment-container relative group rounded-xl overflow-hidden aspect-video bg-slate-900/50';
            div.dataset.attachmentId = attachment.id;
            div.innerHTML = `
                <img src="${attachment.media_url}"
                     alt="${attachment.original_filename}"
                     class="w-full h-full object-contain hover:opacity-90 transition-opacity cursor-zoom-in"
                     data-gallery-id="${attachment.ticket_message_id}"
                     data-gallery-index="${attachment.gallery_index || 0}"
                     onclick="openImageGallery('${attachment.ticket_message_id}', ${attachment.gallery_index || 0})" />

                <div class="absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
                    <div class="flex justify-between items-center">
                        <span class="text-xs text-white truncate max-w-[80%]">${attachment.original_filename}</span>
                        <div class="flex gap-1">
                            <button type="button" onclick="openImageGallery('${attachment.ticket_message_id}', ${attachment.gallery_index || 0})" class="btn btn-xs btn-circle bg-black/50 hover:bg-black/70 border-0 text-white" title="Zoom">
                                <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"></path>
                                </svg>
                            </button>
                            <a href="${attachment.download_url}" class="btn btn-xs btn-circle bg-black/50 hover:bg-black/70 border-0 text-white" title="Download">
                                <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            `;
            return div;
        },

        createVideoElement(attachment) {
            const div = document.createElement('div');
            div.className = 'attachment-container rounded-xl overflow-hidden bg-slate-800/70';
            div.dataset.attachmentId = attachment.id;
            div.innerHTML = `
                <div class="relative aspect-video">
                    <video
                        class="w-full h-full object-contain"
                        preload="metadata"
                        poster="${attachment.thumbnail_url || ''}"
                        data-player-id="video-${attachment.id}">
                        <source src="${attachment.media_url}" type="${attachment.mime_type || 'video/mp4'}">
                        ${ window.translations?.tickets?.video_not_supported || 'Video not supported' }
                    </video>
                    <div class="absolute inset-0 flex items-center justify-center bg-black/30 play-overlay cursor-pointer">
                        <div class="w-16 h-16 rounded-full bg-blue-500/80 flex items-center justify-center">
                            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M8 5v14l11-7z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
                <div class="p-2 bg-slate-700/50">
                    <div class="flex justify-between items-center">
                        <span class="truncate max-w-[200px] text-sm">${attachment.original_filename}</span>
                        <div class="flex gap-1">
                            <button type="button" onclick="openVideoModal('${attachment.media_url}', '${attachment.original_filename}')"
                                    class="btn btn-xs btn-ghost btn-circle" title="${ window.translations?.tickets?.fullscreen || 'Fullscreen' }">
                                <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V6a2 2 0 012-2h2M4 16v2a2 2 0 002 2h2m8-18h2a2 2 0 012 2v2m0 10v2a2 2 0 01-2 2h-2"></path>
                                </svg>
                            </button>
                            <a href="${attachment.download_url}" class="btn btn-xs btn-ghost btn-circle" title="${ window.translations?.tickets?.download || 'Download' }">
                                <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            `;
            return div;
        },

        createAudioElement(attachment) {
            const div = document.createElement('div');
            div.className = 'attachment-container rounded-xl overflow-hidden bg-slate-700/50 p-3';
            div.dataset.attachmentId = attachment.id;
            const mimeType = attachment.mime_type || 'audio/mpeg';
            div.innerHTML = `
                <div class="flex items-center gap-3 mb-2">
                    <svg class="w-5 h-5 text-primary" fill="currentColor" viewBox="0 0 20 20"><path d="M18 3a1 1 0 00-1.196-.98l-10 2A1 1 0 006 5v9.114A4.369 4.369 0 005 14c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V7.82l8-1.6v5.894A4.37 4.37 0 0015 12c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V3z"></path></svg>
                    <span class="font-medium text-sm">${attachment.original_filename}</span>
                </div>
                <audio controls class="w-full" preload="metadata">
                    <source src="${attachment.media_url}" type="${mimeType}">
                    ${ window.translations?.tickets?.audio_not_supported || 'Audio not supported' }
                </audio>
                <div class="flex justify-end mt-2">
                    <a href="${attachment.download_url}" class="btn btn-xs btn-ghost" title="${ window.translations?.tickets?.download || 'Download' }">
                        <svg class="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path></svg>
                        ${ window.translations?.tickets?.download || 'Download' }
                    </a>
                </div>
            `;
            return div;
        },

        createPdfElement(attachment) {
            const div = document.createElement('div');
            div.className = 'attachment-container flex items-center gap-3 p-3 bg-slate-800/70 hover:bg-slate-700/80 rounded-xl transition-all duration-150';
            div.dataset.attachmentId = attachment.id;
            const fileSize = attachment.file_size ? this.humanFileSize(attachment.file_size) : '';
            div.innerHTML = `
                <div class="w-10 h-10 flex-shrink-0 rounded-lg text-red-400 bg-slate-700/50 flex items-center justify-center">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6zm-1 11h-2v2h2v-2zm-2-4h2v3h-2V9z"/>
                        <path d="M14 2v6h6"/>
                    </svg>
                </div>
                <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium truncate">${attachment.original_filename}</p>
                    <p class="text-xs text-slate-400">
                        PDF Document ${fileSize ? '• ' + fileSize : ''}
                    </p>
                </div>
                <div class="flex-shrink-0">
                    <button type="button"
                            class="btn btn-xs btn-ghost"
                            onclick="openPdfModal('${attachment.media_url}', '${attachment.original_filename}')">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                        ${ window.translations?.tickets?.preview || 'Preview' }
                    </button>
                    <a href="${attachment.download_url}" class="btn btn-xs btn-ghost">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                        </svg>
                        ${ window.translations?.tickets?.download || 'Download' }
                    </a>
                </div>
            `;
            return div;
        },

        createGenericFileElement(attachment) {
            const div = document.createElement('div');
            div.className = 'attachment-container flex items-center gap-3 p-3 bg-slate-800/70 hover:bg-slate-700/80 rounded-xl transition-all duration-150';
            div.dataset.attachmentId = attachment.id;
            const fileExtension = attachment.original_filename.split('.').pop()?.toLowerCase() || '';
            const fileSize = attachment.file_size ? this.humanFileSize(attachment.file_size) : '';
            const iconColor = this.getFileIconColor(fileExtension);
            const mediaType = attachment.media_type ? attachment.media_type.charAt(0).toUpperCase() + attachment.media_type.slice(1) : 'File';

            div.innerHTML = `
                <div class="w-10 h-10 flex-shrink-0 rounded-lg ${iconColor} bg-slate-700/50 flex items-center justify-center">
                    <span class="uppercase font-bold text-xs">${fileExtension || '?'}</span>
                </div>
                <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium truncate">${attachment.original_filename}</p>
                    <p class="text-xs text-slate-400">
                        ${mediaType} ${fileSize ? '• ' + fileSize : ''}
                    </p>
                </div>
                <div class="flex-shrink-0">
                    <a href="${attachment.download_url}" class="btn btn-xs btn-ghost">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                        </svg>
                        ${ window.translations?.tickets?.download || 'Download' }
                    </a>
                </div>
            `;
            return div;
        },

        getFileIconColor(extension) {
            switch (extension) {
                case 'pdf': return 'text-red-400';
                case 'doc': case 'docx': return 'text-blue-400';
                case 'xls': case 'xlsx': return 'text-green-400';
                case 'ppt': case 'pptx': return 'text-orange-400';
                case 'zip': case 'rar': case '7z': return 'text-yellow-400';
                case 'txt': case 'log': return 'text-gray-400';
                default: return 'text-purple-400';
            }
        },

        humanFileSize(bytes, decimals = 2) {
            if (bytes <= 0) return '0 B';
            const k = 1024;
            const dm = decimals < 0 ? 0 : decimals;
            const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
        },

        // --- IMAGE GALLERY FUNCTIONS ---
        galleryImages: {},
        currentGalleryId: null,
        currentGalleryIndex: 0,

        buildGalleryData() {
            this.galleryImages = {};
            document.querySelectorAll('[data-gallery-id]').forEach((img, index) => {
                const galleryId = img.getAttribute('data-gallery-id');
                // Use the actual index from the loop in the blade file if available
                const galleryIndex = parseInt(img.getAttribute('data-gallery-index') || index);

                if (!this.galleryImages[galleryId]) {
                    this.galleryImages[galleryId] = [];
                }

                // Ensure array is large enough
                if (this.galleryImages[galleryId].length <= galleryIndex) {
                    this.galleryImages[galleryId].length = galleryIndex + 1;
                }

                this.galleryImages[galleryId][galleryIndex] = {
                    src: img.src, // Use the src from the img tag itself
                    alt: img.alt,
                    element: img // Store reference to the element
                };
            });
            // Clean up sparse arrays
            Object.keys(this.galleryImages).forEach(galleryId => {
                this.galleryImages[galleryId] = this.galleryImages[galleryId].filter(item => item !== undefined);
            });
            console.log('Built gallery data:', this.galleryImages);
        },

        openImageGallery(galleryId, index) {
            this.buildGalleryData(); // Always rebuild before opening

            if (!this.galleryImages[galleryId] || !this.galleryImages[galleryId][index]) {
                console.error('Gallery image not found for', galleryId, index, this.galleryImages);
                // Try finding the image directly if gallery data is wrong
                const imgElement = document.querySelector(`[data-gallery-id="${galleryId}"][data-gallery-index="${index}"]`);
                if (imgElement) {
                    document.getElementById('imageModalContent').src = imgElement.src;
                    document.getElementById('imageModalTitle').textContent = imgElement.alt;
                    document.getElementById('imageModalNavigation').classList.add('hidden'); // Hide nav if data is broken
                    document.getElementById('imageModal').classList.remove('hidden');
                    document.body.classList.add('overflow-hidden');
                } else {
                    alert('Error opening image.');
                }
                return;
            }

            this.currentGalleryId = galleryId;
            this.currentGalleryIndex = index;
            const image = this.galleryImages[galleryId][index];
            const totalImages = this.galleryImages[galleryId].length;

            document.getElementById('imageModalContent').src = image.src;
            document.getElementById('imageModalTitle').textContent = image.alt;

            const navControls = document.getElementById('imageModalNavigation');
            if (navControls) {
                if (totalImages > 1) {
                    navControls.classList.remove('hidden');
                    document.getElementById('imagePrevButton').onclick = () => this.navigateGallery(-1);
                    document.getElementById('imageNextButton').onclick = () => this.navigateGallery(1);
                    document.getElementById('imageCounter').textContent = `${index + 1} / ${totalImages}`;
                } else {
                    navControls.classList.add('hidden');
                }
            }

            document.getElementById('imageModal').classList.remove('hidden');
            document.body.classList.add('overflow-hidden');
        },

        navigateGallery(direction) {
            if (!this.currentGalleryId || !this.galleryImages[this.currentGalleryId]) return;

            const gallery = this.galleryImages[this.currentGalleryId];
            const totalImages = gallery.length;
            const newIndex = (this.currentGalleryIndex + direction + totalImages) % totalImages;

            this.openImageGallery(this.currentGalleryId, newIndex);
        },

        closeImageModal() {
            document.getElementById('imageModal').classList.add('hidden');
            document.body.classList.remove('overflow-hidden');
            this.currentGalleryId = null;
        },

        // --- VIDEO PLAYER FUNCTIONS ---
        initCustomVideoPlayers() {
            document.querySelectorAll('video[data-player-id]').forEach(video => {
                const container = video.closest('.attachment-container');
                if (!container) return;

                const overlay = container.querySelector('.play-overlay');
                if (!overlay) return;

                // Ensure event listeners aren't added multiple times
                if (video.dataset.customPlayerInit) return;
                video.dataset.customPlayerInit = true;

                const playHandler = () => {
                    if (video.paused) {
                        video.play();
                    } else {
                        video.pause();
                    }
                };

                overlay.addEventListener('click', playHandler);
                // Also allow clicking the video itself to play/pause
                video.addEventListener('click', playHandler);

                video.addEventListener('pause', () => overlay.classList.remove('hidden'));
                video.addEventListener('play', () => overlay.classList.add('hidden'));
                video.addEventListener('ended', () => overlay.classList.remove('hidden')); // Show overlay when ended
            });
        },

        // --- OTHER UTILITIES ---
        setupNotifications() {
            // Request notification permission if not already granted
            if ('Notification' in window && Notification.permission !== 'granted' && Notification.permission !== 'denied') {
                // Add a button to request permission
                const notificationButton = document.createElement('button');
                notificationButton.className = 'fixed top-20 right-4 z-30 bg-blue-600 text-white text-xs font-medium px-3 py-1.5 rounded-lg shadow-lg flex items-center gap-1.5';
                notificationButton.innerHTML = `
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m-5-15 2-1v6m-2 0h4"/>
                    </svg>
                    <span>Enable Notifications</span>
                `;

                notificationButton.addEventListener('click', function() {
                    Notification.requestPermission().then(function(permission) {
                        if (permission === 'granted') {
                            notificationButton.remove();
                        }
                    });
                });

                document.body.appendChild(notificationButton);
            }
        },

        playNotificationSound() {
            // Check if sound is enabled
            const soundToggle = document.getElementById('sound-toggle');
            if (!soundToggle || !soundToggle.checked) {
                console.log('Sound is disabled');
                return;
            }

            const notificationSound = document.getElementById('notification-sound');
            if (notificationSound) {
                // Reset the audio to the beginning
                notificationSound.currentTime = 0;
                // Play the sound
                notificationSound.play()
                    .then(() => console.log('Notification sound played successfully'))
                    .catch(e => console.error('Error playing sound:', e));
            } else {
                console.error('Notification sound element not found');
            }
        },

        showNewMessageIndicator() {
            const messagesContainer = document.getElementById('messages-container');
            const newMessageIndicator = document.getElementById('new-message-indicator');
            if (!messagesContainer || !newMessageIndicator) return;

            // Check if we're already at the bottom
            const isAtBottom = messagesContainer.scrollTop >= (messagesContainer.scrollHeight - messagesContainer.clientHeight - 100);

            // If we're not at the bottom, show the indicator
            if (!isAtBottom) {
                newMessageIndicator.classList.remove('hidden');

                // Auto-hide after 5 seconds
                setTimeout(() => {
                    newMessageIndicator.classList.add('hidden');
                }, 5000);
            }
        }
    }
}

// Global functions needed for onclick attributes
function openImageGallery(galleryId, index) {
    // Always use the standalone implementation for consistency
    buildGalleryData(); // Always rebuild before opening

    if (!galleryImages[galleryId] || !galleryImages[galleryId][index]) {
        console.error('Gallery image not found for', galleryId, index, galleryImages);
        // Try finding the image directly if gallery data is wrong
        const imgElement = document.querySelector(`[data-gallery-id="${galleryId}"][data-gallery-index="${index}"]`);
        if (imgElement) {
            document.getElementById('imageModalContent').src = imgElement.src;
            document.getElementById('imageModalTitle').textContent = imgElement.alt;
            document.getElementById('imageModalNavigation').classList.add('hidden'); // Hide nav if data is broken
            document.getElementById('imageModal').classList.remove('hidden');
            document.body.classList.add('overflow-hidden');
        } else {
            alert('Error opening image.');
        }
        return;
    }

    currentGalleryId = galleryId;
    currentGalleryIndex = index;
    const image = galleryImages[galleryId][index];
    const totalImages = galleryImages[galleryId].length;

    document.getElementById('imageModalContent').src = image.src;
    document.getElementById('imageModalTitle').textContent = image.alt;

    const navControls = document.getElementById('imageModalNavigation');
    if (navControls) {
        if (totalImages > 1) {
            navControls.classList.remove('hidden');
            document.getElementById('imagePrevButton').onclick = () => navigateGallery(-1);
            document.getElementById('imageNextButton').onclick = () => navigateGallery(1);
            document.getElementById('imageCounter').textContent = `${index + 1} / ${totalImages}`;
        } else {
            navControls.classList.add('hidden');
        }
    }

    document.getElementById('imageModal').classList.remove('hidden');
    document.body.classList.add('overflow-hidden');
}

function closeImageModal() {
    // Always use the standalone implementation for consistency
    document.getElementById('imageModal').classList.add('hidden');
    document.body.classList.remove('overflow-hidden');
    currentGalleryId = null;
}

// Standalone gallery navigation function
function navigateGallery(direction) {
    if (!currentGalleryId || !galleryImages[currentGalleryId]) return;

    const gallery = galleryImages[currentGalleryId];
    const totalImages = gallery.length;
    const newIndex = (currentGalleryIndex + direction + totalImages) % totalImages;

    openImageGallery(currentGalleryId, newIndex);
}

// Add other global modal functions (openVideoModal, openPdfModal) if they aren't part of the Alpine component
// Example:
function openVideoModal(url, title) {
    document.getElementById('videoModalContent').src = url;
    document.getElementById('videoModalTitle').textContent = title;
    document.getElementById('videoModal').classList.remove('hidden');
    document.body.classList.add('overflow-hidden');
}

function closeVideoModal() {
    const video = document.getElementById('videoModalContent');
    video.pause();
    video.src = ''; // Clear src to stop loading
    document.getElementById('videoModal').classList.add('hidden');
    document.body.classList.remove('overflow-hidden');
}

function openPdfModal(url, title) {
    document.getElementById('pdfModalContent').src = url;
    document.getElementById('pdfModalTitle').textContent = title;
    document.getElementById('pdfModal').classList.remove('hidden');
    document.body.classList.add('overflow-hidden');
}

function closePdfModal() {
    const iframe = document.getElementById('pdfModalContent');
    iframe.src = 'about:blank'; // Clear src
    document.getElementById('pdfModal').classList.add('hidden');
    document.body.classList.remove('overflow-hidden');
}


// Initialize video players and gallery when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Wait for Alpine and Livewire to be fully initialized
    setTimeout(() => {
        initializeComponents();
    }, 500);
});

// Function to initialize components safely
function initializeComponents() {
    // Initialize standalone functions for when Alpine component isn't accessible
    initCustomVideoPlayers();
    buildGalleryData();

    // Try to scroll to bottom if the messages container exists
    const messagesContainer = document.getElementById('messages-container');
    if (messagesContainer) {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
}

// Re-initialize on Livewire updates
document.addEventListener('livewire:navigated', () => {
    setTimeout(() => {
        initializeComponents();
    }, 200);
});

// Hook into Livewire's morph events if available
if (typeof window.Livewire !== 'undefined') {
    // For Livewire 3
    document.addEventListener('livewire:initialized', () => {
        Livewire.hook('morph.updated', ({ el }) => {
            if (el.id === 'ticket-view' || el.querySelector('#messages-container')) {
                setTimeout(() => {
                    initializeComponents();
                }, 100);
            }
        });
    });
}

// Standalone functions for when Alpine.js is not available
// Assign to window object to make them globally available
window.initCustomVideoPlayers = function() {
    document.querySelectorAll('video[data-player-id]').forEach(video => {
        const container = video.closest('.attachment-container');
        if (!container) return;

        const overlay = container.querySelector('.play-overlay');
        if (!overlay) return;

        // Ensure event listeners aren't added multiple times
        if (video.dataset.customPlayerInit) return;
        video.dataset.customPlayerInit = true;

        const playHandler = () => {
            if (video.paused) {
                video.play();
            } else {
                video.pause();
            }
        };

        overlay.addEventListener('click', playHandler);
        // Also allow clicking the video itself to play/pause
        video.addEventListener('click', playHandler);

        video.addEventListener('pause', () => overlay.classList.remove('hidden'));
        video.addEventListener('play', () => overlay.classList.add('hidden'));
        video.addEventListener('ended', () => overlay.classList.remove('hidden')); // Show overlay when ended
    });
}

// Gallery data for standalone mode
let galleryImages = {};
let currentGalleryId = null;
let currentGalleryIndex = 0;

// Assign to window object to make it globally available
window.buildGalleryData = function() {
    galleryImages = {};
    document.querySelectorAll('[data-gallery-id]').forEach((img, index) => {
        const galleryId = img.getAttribute('data-gallery-id');
        // Use the actual index from the loop in the blade file if available
        const galleryIndex = parseInt(img.getAttribute('data-gallery-index') || index);

        if (!galleryImages[galleryId]) {
            galleryImages[galleryId] = [];
        }

        // Ensure array is large enough
        if (galleryImages[galleryId].length <= galleryIndex) {
            galleryImages[galleryId].length = galleryIndex + 1;
        }

        galleryImages[galleryId][galleryIndex] = {
            src: img.src, // Use the src from the img tag itself
            alt: img.alt,
            element: img // Store reference to the element
        };
    });
    // Clean up sparse arrays
    Object.keys(galleryImages).forEach(galleryId => {
        galleryImages[galleryId] = galleryImages[galleryId].filter(item => item !== undefined);
    });
    console.log('Built gallery data:', galleryImages);
}

// Function to handle attachment updates from WebSocket events
// Assign to window object to make it globally available
window.handleAttachmentUpdates = function(messageId, attachments) {
    console.log('Standalone: Attachments ready for message:', messageId, attachments);

    if (!attachments || !Array.isArray(attachments) || attachments.length === 0) {
        console.warn('No valid attachments received for message:', messageId);
        return;
    }

    // Track if any attachments were updated
    let updatedAny = false;

    attachments.forEach(attachment => {
        if (!attachment || !attachment.id) {
            console.warn('Invalid attachment data:', attachment);
            return;
        }

        const attachmentElement = document.querySelector(`[data-attachment-id="${attachment.id}"]`);
        if (!attachmentElement) {
            console.warn(`Attachment element not found for ID: ${attachment.id}`);
            return;
        }

        // Find the container to replace (the parent div)
        const containerToReplace = attachmentElement.closest('.attachment-container');
        if (!containerToReplace) {
            console.warn(`Container not found for attachment ID: ${attachment.id}`);
            return;
        }

        try {
            // Create a new element based on the attachment type
            let newElement;

            if (attachment.is_image) {
                newElement = createImageElement(attachment);
            } else if (attachment.is_video) {
                newElement = createVideoElement(attachment);
            } else if (attachment.is_audio) {
                newElement = createAudioElement(attachment);
            } else if (attachment.is_pdf) {
                newElement = createPdfElement(attachment);
            } else {
                newElement = createGenericFileElement(attachment);
            }

            // Replace the old container with the new one
            if (newElement) {
                containerToReplace.replaceWith(newElement);
                updatedAny = true;
                console.log(`Successfully updated attachment ID: ${attachment.id}`);
            }
        } catch (error) {
            console.error(`Error updating attachment ID: ${attachment.id}`, error);
            // Show error state instead of failing silently
            containerToReplace.innerHTML = `
                <div class="flex items-center justify-center gap-2 px-3 py-4 text-xs font-medium bg-red-900/30 text-red-300 rounded-xl">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span>Error displaying attachment</span>
                </div>
            `;
        }
    });

    // Only re-initialize if we actually updated something
    if (updatedAny) {
        // Re-initialize things if needed (e.g., custom video players, gallery)
        initCustomVideoPlayers();
        buildGalleryData(); // Rebuild gallery data after updates
        console.log('Re-initialized video players and gallery data');
    }
}

// Helper functions for creating attachment elements
// Assign to window object to make them globally available
window.createImageElement = function(attachment) {
    const div = document.createElement('div');
    div.className = 'attachment-container relative group rounded-xl overflow-hidden aspect-video bg-slate-900/50';
    div.dataset.attachmentId = attachment.id;
    div.innerHTML = `
        <img src="${attachment.media_url}"
             alt="${attachment.original_filename}"
             class="w-full h-full object-contain hover:opacity-90 transition-opacity cursor-zoom-in"
             data-gallery-id="${attachment.ticket_message_id}"
             data-gallery-index="${attachment.gallery_index || 0}"
             onclick="openImageGallery('${attachment.ticket_message_id}', ${attachment.gallery_index || 0})" />

        <div class="absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
            <div class="flex justify-between items-center">
                <span class="text-xs text-white truncate max-w-[80%]">${attachment.original_filename}</span>
                <div class="flex gap-1">
                    <button type="button" onclick="openImageGallery('${attachment.ticket_message_id}', ${attachment.gallery_index || 0})" class="btn btn-xs btn-circle bg-black/50 hover:bg-black/70 border-0 text-white" title="Zoom">
                        <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"></path>
                        </svg>
                    </button>
                    <a href="${attachment.download_url}" class="btn btn-xs btn-circle bg-black/50 hover:bg-black/70 border-0 text-white" title="Download">
                        <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    `;
    return div;
}

window.createVideoElement = function(attachment) {
    const div = document.createElement('div');
    div.className = 'attachment-container rounded-xl overflow-hidden bg-slate-800/70';
    div.dataset.attachmentId = attachment.id;
    div.innerHTML = `
        <div class="relative aspect-video">
            <video
                class="w-full h-full object-contain"
                preload="metadata"
                poster="${attachment.thumbnail_url || ''}"
                data-player-id="video-${attachment.id}">
                <source src="${attachment.media_url}" type="${attachment.mime_type || 'video/mp4'}">
                Video not supported
            </video>
            <div class="absolute inset-0 flex items-center justify-center bg-black/30 play-overlay cursor-pointer">
                <div class="w-16 h-16 rounded-full bg-blue-500/80 flex items-center justify-center">
                    <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M8 5v14l11-7z"></path>
                    </svg>
                </div>
            </div>
        </div>
        <div class="p-2 bg-slate-700/50">
            <div class="flex justify-between items-center">
                <span class="truncate max-w-[200px] text-sm">${attachment.original_filename}</span>
                <div class="flex gap-1">
                    <button type="button" onclick="openVideoModal('${attachment.media_url}', '${attachment.original_filename}')"
                            class="btn btn-xs btn-ghost btn-circle" title="Fullscreen">
                        <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V6a2 2 0 012-2h2M4 16v2a2 2 0 002 2h2m8-18h2a2 2 0 012 2v2m0 10v2a2 2 0 01-2 2h-2"></path>
                        </svg>
                    </button>
                    <a href="${attachment.download_url}" class="btn btn-xs btn-ghost btn-circle" title="Download">
                        <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    `;
    return div;
}

window.createAudioElement = function(attachment) {
    const div = document.createElement('div');
    div.className = 'attachment-container rounded-xl overflow-hidden bg-slate-700/50 p-3';
    div.dataset.attachmentId = attachment.id;
    div.innerHTML = `
        <div class="flex items-center gap-3 mb-2">
            <svg class="w-5 h-5 text-primary" fill="currentColor" viewBox="0 0 20 20"><path d="M18 3a1 1 0 00-1.196-.98l-10 2A1 1 0 006 5v9.114A4.369 4.369 0 005 14c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V7.82l8-1.6v5.894A4.37 4.37 0 0015 12c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V3z"></path></svg>
            <span class="font-medium text-sm truncate">${attachment.original_filename}</span>
        </div>
        <audio controls class="w-full" preload="metadata">
            <source src="${attachment.media_url}" type="${attachment.mime_type || 'audio/mpeg'}">
            Audio not supported
        </audio>
        <div class="flex justify-end mt-2">
            <a href="${attachment.download_url}" class="btn btn-xs btn-ghost" title="Download">
                <svg class="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path></svg>
                Download
            </a>
        </div>
    `;
    return div;
}

window.createPdfElement = function(attachment) {
    const div = document.createElement('div');
    div.className = 'attachment-container flex items-center gap-3 p-3 bg-slate-800/70 hover:bg-slate-700/80 rounded-xl transition-all duration-150';
    div.dataset.attachmentId = attachment.id;
    div.innerHTML = `
        <div class="w-10 h-10 flex-shrink-0 rounded-lg text-red-400 bg-slate-700/50 flex items-center justify-center">
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6zm-1 11h-2v2h2v-2zm-2-4h2v3h-2V9z"/>
                <path d="M14 2v6h6"/>
            </svg>
        </div>
        <div class="flex-1 min-w-0">
            <p class="text-sm font-medium truncate">${attachment.original_filename}</p>
            <p class="text-xs text-slate-400">
                PDF Document
            </p>
        </div>
        <div class="flex-shrink-0">
            <button type="button"
                    class="btn btn-xs btn-ghost"
                    onclick="openPdfModal('${attachment.media_url}', '${attachment.original_filename}')">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                Preview
            </button>
            <a href="${attachment.download_url}" class="btn btn-xs btn-ghost">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                </svg>
                Download
            </a>
        </div>
    `;
    return div;
}

window.createGenericFileElement = function(attachment) {
    const div = document.createElement('div');
    div.className = 'attachment-container flex items-center gap-3 p-3 bg-slate-800/70 hover:bg-slate-700/80 rounded-xl transition-all duration-150';
    div.dataset.attachmentId = attachment.id;
    const fileExtension = attachment.original_filename.split('.').pop()?.toLowerCase() || '';
    const iconColor = getFileIconColor(fileExtension);
    div.innerHTML = `
        <div class="w-10 h-10 flex-shrink-0 rounded-lg ${iconColor} bg-slate-700/50 flex items-center justify-center">
            <span class="uppercase font-bold text-xs">${fileExtension || '?'}</span>
        </div>
        <div class="flex-1 min-w-0">
            <p class="text-sm font-medium truncate">${attachment.original_filename}</p>
            <p class="text-xs text-slate-400">
                Document
            </p>
        </div>
        <div class="flex-shrink-0">
            <a href="${attachment.download_url}" class="btn btn-xs btn-ghost">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                </svg>
                Download
            </a>
        </div>
    `;
    return div;
}

window.getFileIconColor = function(extension) {
    switch (extension) {
        case 'pdf': return 'text-red-400';
        case 'doc': case 'docx': return 'text-blue-400';
        case 'xls': case 'xlsx': return 'text-green-400';
        case 'ppt': case 'pptx': return 'text-orange-400';
        case 'zip': case 'rar': case '7z': return 'text-yellow-400';
        case 'txt': case 'log': return 'text-gray-400';
        default: return 'text-purple-400';
    }
}

// Add translations object if needed for JS
// window.translations = { tickets: { ... } }; // Populate this from Blade if necessary

// Expose the ticketView function to the global scope for Alpine.js to access it
window.ticketView = ticketView;
