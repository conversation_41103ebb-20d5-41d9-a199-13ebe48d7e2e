<?php

return [
    // Application Form Progress
    'step_of' => 'Schritt :current von :total',
    'completed' => ':percent% abgeschlossen',
    'personal_data' => 'Persönliche Daten',
    'role_specific' => 'Rollenspezifisch',
    'about_you' => 'Über dich',
    'first_step_description' => 'Bitte gib deine persönlichen Informationen ein, um den Bewerbungsprozess zu beginnen.',
    'application_form' => 'Bewerbungsformular',
    'application_description' => 'Werde Teil unseres kreativen Teams und hilf dabei, fantastische Minecraft-Inhalte zum Leben zu erwecken.',
    'personal_data_description' => 'Erzähle uns ein wenig über dich, um zu beginnen.',
    'review' => 'Überprüfen',

    // Form Fields - General
    'name' => 'Name',
    'name_help' => 'Wie sollen wir dich nennen?',
    'name_placeholder' => 'Dein bevorzugter Name',
    'name_description' => 'Dies kann dein Discord-Name, Nickname oder echter Name sein - wie du möchtest.',

    // Professions
    'professions' => 'Tätigkeiten',
    'professions_help' => 'Wähle mindestens eine aus',
    'professions_select_at_least_one' => 'Mindestens einen auswählen',
    'professions_question' => 'Welche Rolle(n) interessieren dich?',
    'professions_select_description' => 'Wähle alle Positionen aus, für die du dich bewerben möchtest.',
    'profession_description' => [
        'actor' => 'Schauspieler',
        'actor_no_voice' => 'Schauspieler (No voice)',
        'voice_actor' => 'Synchronsprecher',
        'builder' => 'Builder',
        'designer' => 'Designer',
        'cutter' => 'Cutter',
        'cameraman' => 'Kameramann',
        'developer' => 'Developer',
        'modeler' => '3D-Modellierer',
        'music_producer' => 'Musikproduzent',
        'other' => 'Andere'
    ],
    'profession_subtitle' => [
        'actor' => 'Stimme + Performance',
        'actor_no_voice' => 'Nur Performance',
        'voice_actor' => 'Nur Stimme',
        'builder' => 'Architektur & Design',
        'designer' => 'Grafik & Visuell',
        'cutter' => 'Videoschnitt',
        'cameraman' => 'Aufnahme & Filming',
        'developer' => 'Mods & Programmierung',
        'modeler' => '3D-Modelle & Assets',
        'music_producer' => 'Audio & Musik',
        'other' => 'Individuelle Rolle'
    ],
    'profession_description_long' => [
        'actor' => 'Erwecke Charaktere mit Schauspiel und Sprachaufnahmen zum Leben',
        'actor_no_voice' => 'Fokussiere dich auf Schauspiel und Charakterdarstellung ohne Spracharbeit',
        'voice_actor' => 'Stelle Sprachaufnahmen für Charaktere ohne Schauspiel bereit',
        'builder' => 'Erstelle beeindruckende Bauten und architektonische Designs für unsere Welt',
        'designer' => 'Gestalte Grafiken, Thumbnails und visuellen Content für unsere Marke',
        'cutter' => 'Schneide und produziere hochwertige Videoinhalte für unsere Kanäle',
        'cameraman' => 'Nimm fantastische Aufnahmen auf und verwalte Aufnahmesessions',
        'developer' => 'Entwickle Mods und technische Lösungen für unseren Minecraft-Server',
        'modeler' => 'Erstelle 3D-Modelle, Texturen und Assets für unsere Projekte',
        'music_producer' => 'Komponiere und produziere Musik und Soundeffekte für unseren Content',
        'other' => 'Bringe deine einzigartigen Fähigkeiten und Talente in unser kreatives Team ein'
    ],
    'other_profession' => 'Andere Tätigkeit',
    'other_profession_help' => 'Bitte genauer beschreiben',
    'other_profession_placeholder' => 'Bitte spezifizieren',

    // Profession Descriptions
//    'profession_description' => [
//        'actor' => 'Schauspieler für Videoaufnahmen mit Sprachaufnahmen',
//        'actor_no_voice' => 'Schauspieler für Videoaufnahmen ohne Sprachaufnahmen',
//        'voice_actor' => 'Sprecher für Synchronisationen und Voice-Over',
//        'builder' => 'Erstellt und gestaltet 3D-Umgebungen und Strukturen',
//        'designer' => 'Gestaltet grafische Elemente wie Logos, Thumbnails, etc.',
//        'cutter' => 'Videoschnitt und Postproduktion',
//        'cameraman' => 'Bedient Kameras für Videoaufnahmen',
//        'developer' => 'Programmiert Anwendungen und Websites',
//        'modeler' => 'Erstellt 3D-Modelle für Charaktere und Objekte',
//        'music_producer' => 'Komponiert und produziert Musik und Soundeffekte',
//        'other' => 'Andere Tätigkeit - bitte spezifizieren',
//    ],

    // Personal Information
    'age' => 'Alter',
    'age_placeholder' => 'Dein Alter',
    'age_help' => 'Wir benötigen dein Alter für die Rollenzuweisung.',
    'gender' => 'Geschlecht',
    'gender_select' => 'Option auswählen',
    'gender_no_info' => 'Keine Angabe',
    'gender_male' => 'Männlich',
    'gender_female' => 'Weiblich',
    'gender_diverse' => 'Divers',
    'gender_other' => 'Anderes',
    'pronouns' => 'Pronomen',
    'pronouns_placeholder' => 'z.B. er/ihm',

    // Availability
    'confirm_availability' => 'Erreichbarkeit bestätigen',
    'availability_confirmation_text' => 'Ich bestätige, dass ich innerhalb von 48 Stunden auf Discord antworten kann und über die Bewerbungsphase hinweg erreichbar bin.',

    // Form Controls
    'next_step' => 'Nächster Schritt',
    'previous_step' => 'Vorheriger Schritt',
    'submit_application' => 'Bewerbung absenden',
    'required_fields_notice' => 'Alle mit * markierten Felder sind Pflichtangaben',

    // Role-Specific Questions
    'role_questions' => 'Rollenspezifische Fragen',
    'portfolio' => 'Portfolio',
    'portfolio_help' => 'Links zu deinen bisherigen Arbeiten',
    'portfolio_placeholder' => 'URL zu deinem Portfolio oder Beispielen deiner Arbeit',

    // About You
    'experience' => 'Erfahrung',
    'experience_help' => 'Erzähl uns von deiner Erfahrung',
    'experience_placeholder' => 'Deine Erfahrung in den ausgewählten Rollen',
    'motivation' => 'Motivation',
    'motivation_help' => 'Warum möchtest du mitmachen?',
    'motivation_placeholder' => 'Warum interessierst du dich für eine Mitarbeit in unserem Team?',
    'about_you_text' => 'Über dich',
    'about_you_placeholder' => 'Erzähle uns etwas über dich (mindestens 50 Zeichen)',
    'strengths_weaknesses' => 'Stärken & Schwächen',
    'strengths_weaknesses_placeholder' => 'Beschreibe deine Stärken und Schwächen (mindestens 50 Zeichen)',
    'final_words' => 'Abschließende Worte',
    'final_words_placeholder' => 'Möchtest du uns noch etwas mitteilen?',
    'characters' => 'Zeichen',

    // Review & Submit
    'review_submit' => 'Überprüfen & Absenden',
    'confirm_correct_information' => 'Ich bestätige, dass alle angegebenen Informationen korrekt sind',
    'not_specified' => 'Nicht angegeben',

    // Role-specific fields
    'acting_voice_acting' => 'Audio & Voice',
    'voice_type' => 'Stimmtyp',
    'voice_type_deep' => 'Tief',
    'voice_type_medium' => 'Mittel',
    'voice_type_high' => 'Hoch',
    'microphone' => 'Mikrofon',
    'microphone_placeholder' => 'Dein Mikrofon',

    'builder_cameraman' => 'Pc Statistic',
    'ram' => 'RAM',
    'ram_placeholder' => 'z.B. 16GB',
    'fps' => 'FPS in Minecraft',
    'fps_placeholder' => 'Durchschnittliche FPS',
    'gpu' => 'Grafikkarte',
    'gpu_placeholder' => 'Deine Grafikkarte',

    'designer' => 'Designer',
    'design_programs' => 'Design-Programme',
    'design_programs_placeholder' => 'z.B. Photoshop, Illustrator',
    'design_style' => 'Design-Stil',
    'design_style_placeholder' => 'Dein bevorzugter Design-Stil',
    'favorite_design' => 'Favorisiertes Design',
    'favorite_design_placeholder' => 'Ein Design, das dich inspiriert',
    'portfolio_link' => 'Portfolio-Link',
    'portfolio_link_placeholder' => 'Link zu deinem Portfolio (optional)',

    'developer' => 'Developer',
    'programming_languages' => 'Programmiersprachen',
    'programming_languages_placeholder' => 'z.B. Java, JavaScript, Python',
    'preferred_ide' => 'Bevorzugte IDE',
    'preferred_ide_placeholder' => 'z.B. VSCode, IntelliJ',

    'music_producer' => 'Musikproduzent',
    'daw' => 'DAW',
    'daw_placeholder' => 'z.B. FL Studio, Ableton',

    'desired_role' => 'Angestrebte Rolle',
    'desired_role_placeholder' => 'Beschreibe deine Wunschrolle im Team (optional)',

    // Processing
    'processing' => 'Wird verarbeitet...',

    // Submission
    'application_submitted' => 'Bewerbung eingereicht',
    'application_updated' => 'Bewerbung aktualisiert',
    'thank_you' => 'Vielen Dank für deine Bewerbung!',
    'we_will_contact' => 'Wir werden deine Bewerbung prüfen und dich über Discord kontaktieren.',
    'application_not_editable' => 'Diese Bewerbung kann derzeit nicht bearbeitet werden.',
    'edit_application' => 'Bewerbung bearbeiten',

    // Errors
    'please_select_at_least_one_profession' => 'Bitte gehe zurück und wähle mindestens einen Beruf aus.',
    'application_not_found_or_no_access' => 'Bewerbung nicht gefunden oder du hast keinen Zugriff darauf.',
    'filtering_error' => 'Fehler bei der Filterung: :error',
    'created_at' => 'Erstellt am',
    'updated_at' => 'Aktualisiert am',
    'status' => 'Status',
    'age' => 'Alter',
    'age_placeholder' => 'Dein Alter',
    'gender' => 'Geschlecht',
    'gender_select' => 'Bitte wählen',
    'gender_male' => 'Männlich',
    'gender_female' => 'Weiblich',
    'gender_diverse' => 'Divers',
    'gender_no_info' => 'Keine Angabe',
    'confirmation' => 'Bestätigung',
    'previous_step' => 'Zurück',
    'next_step' => 'Weiter',
    'submit_application' => 'Bewerbung absenden'
];
