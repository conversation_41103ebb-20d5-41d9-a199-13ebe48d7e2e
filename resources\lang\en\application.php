<?php

return [
    // Application Form Progress
    'step_of' => 'Step :current of :total',
    'completed' => ':percent% Completed',
    'personal_data' => 'Personal Data',
    'role_specific' => 'Role-Specific',
    'about_you' => 'About You',
    'first_step_description' => 'Please provide your personal information to begin the application process.',
    'application_form' => 'Application Form',
    'application_description' => 'Join our creative team and help bring amazing Minecraft content to life.',
    'personal_data_description' => 'Tell us a bit about yourself to get started.',
    'review' => 'Review',

    // Form Fields - General
    'name' => 'Name',
    'name_help' => 'What should we call you?',
    'name_placeholder' => 'Your preferred name',
    'name_description' => 'This can be your Discord name, nickname, or real name - your choice.',

    // Professions
    'professions' => 'Professions',
    'professions_help' => 'Choose at least one',
    'professions_select_at_least_one' => 'Select at least one',
    'professions_question' => 'What role(s) interest you?',
    'professions_select_description' => 'Select all positions you\'d like to apply for.',
    'profession_description' => [
        'actor' => 'Actor',
        'actor_no_voice' => 'Actor (No voice)',
        'voice_actor' => 'Voice Actor',
        'builder' => 'Builder',
        'designer' => 'Designer',
        'cutter' => 'Cutter',
        'cameraman' => 'Cameraman',
        'developer' => 'Developer',
        'modeler' => '3D Modeler',
        'music_producer' => 'Music Producer',
        'other' => 'Other'
    ],
    'profession_subtitle' => [
        'actor' => 'Voice + Performance',
        'actor_no_voice' => 'Performance Only',
        'voice_actor' => 'Voice Only',
        'builder' => 'Architecture & Design',
        'designer' => 'Graphics & Visual',
        'cutter' => 'Video Editing',
        'cameraman' => 'Recording & Filming',
        'developer' => 'Mods & Programming',
        'modeler' => '3D Models & Assets',
        'music_producer' => 'Audio & Music',
        'other' => 'Custom Role'
    ],
    'profession_description_long' => [
        'actor' => 'Bring characters to life with voice acting and in-game performance',
        'actor_no_voice' => 'Focus on in-game acting and character performance without voice work',
        'voice_actor' => 'Provide voice work for characters without in-game performance',
        'builder' => 'Create stunning builds and architectural designs for our world',
        'designer' => 'Design graphics, thumbnails, and visual content for our brand',
        'cutter' => 'Edit and produce high-quality video content for our channels',
        'cameraman' => 'Capture amazing footage and manage recording sessions',
        'developer' => 'Develop mods and technical solutions for our Minecraft server',
        'modeler' => 'Create 3D models, textures, and assets for our projects',
        'music_producer' => 'Compose and produce music and sound effects for our content',
        'other' => 'Bring your unique skills and talents to our creative team'
    ],
    'other_profession' => 'Other Profession',
    'other_profession_help' => 'Please describe in detail',
    'other_profession_placeholder' => 'Please specify',

    // Profession Descriptions
//    'profession_description' => [
//        'actor' => 'Actor for video recordings with voice recordings',
//        'actor_no_voice' => 'Actor for video recordings without voice recordings',
//        'voice_actor' => 'Voice actor for dubbing and voice-over',
//        'builder' => 'Creates and designs 3D environments and structures',
//        'designer' => 'Designs graphic elements like logos, thumbnails, etc.',
//        'cutter' => 'Video editing and post-production',
//        'cameraman' => 'Operates cameras for video recordings',
//        'developer' => 'Programs applications and websites',
//        'modeler' => 'Creates 3D models for characters and objects',
//        'music_producer' => 'Composes and produces music and sound effects',
//        'other' => 'Other profession - please specify',
//    ],

    // Personal Information
    'age' => 'Age',
    'age_placeholder' => 'Your age',
    'age_help' => 'We need to know your age for role assignment purposes.',
    'gender' => 'Gender',
    'gender_select' => 'Select an option',
    'gender_no_info' => 'Prefer not to say',
    'gender_male' => 'Male',
    'gender_female' => 'Female',
    'gender_diverse' => 'Diverse',
    'gender_other' => 'Other',
    'pronouns' => 'Pronouns',
    'pronouns_placeholder' => 'e.g., he/him',

    // Availability
    'confirm_availability' => 'Confirm Availability',
    'availability_confirmation_text' => 'I confirm that I can respond on Discord within 48 hours and remain reachable throughout the application process.',

    // Form Controls
    'next_step' => 'Next Step',
    'previous_step' => 'Previous Step',
    'submit_application' => 'Submit Application',
    'required_fields_notice' => 'All fields marked with * are required',

    // Role-Specific Questions
    'role_questions' => 'Role-Specific Questions',
    'portfolio' => 'Portfolio',
    'portfolio_help' => 'Links to your previous work',
    'portfolio_placeholder' => 'URL to your portfolio or examples of your work',

    // About You
    'experience' => 'Experience',
    'experience_help' => 'Tell us about your experience',
    'experience_placeholder' => 'Your experience in the selected roles',
    'motivation' => 'Motivation',
    'motivation_help' => 'Why do you want to join?',
    'motivation_placeholder' => 'Why are you interested in joining our team?',
    'about_you_text' => 'About you',
    'about_you_placeholder' => 'Tell us something about yourself (minimum 50 characters)',
    'strengths_weaknesses' => 'Strengths & Weaknesses',
    'strengths_weaknesses_placeholder' => 'Describe your strengths and weaknesses (minimum 50 characters)',
    'final_words' => 'Final Words',
    'final_words_placeholder' => 'Would you like to tell us anything else?',
    'characters' => 'characters',

    // Review & Submit
    'review_submit' => 'Review & Submit',
    'confirm_correct_information' => 'I confirm that all information provided is correct',
    'not_specified' => 'Not specified',

    // Role-specific fields
    'acting_voice_acting' => 'Audio & Voice',
    'voice_type' => 'Voice Type',
    'voice_type_deep' => 'Deep',
    'voice_type_medium' => 'Medium',
    'voice_type_high' => 'High',
    'microphone' => 'Microphone',
    'microphone_placeholder' => 'Your microphone',

    'builder_cameraman' => 'Pc Stats',
    'ram' => 'RAM',
    'ram_placeholder' => 'e.g. 16GB',
    'fps' => 'FPS in Minecraft',
    'fps_placeholder' => 'Average FPS',
    'gpu' => 'Graphics Card',
    'gpu_placeholder' => 'Your graphics card',

    'designer' => 'Desginer',
    'design_programs' => 'Design Programs',
    'design_programs_placeholder' => 'e.g. Photoshop, Illustrator',
    'design_style' => 'Design Style',
    'design_style_placeholder' => 'Your preferred design style',
    'favorite_design' => 'Favorite Design',
    'favorite_design_placeholder' => 'A design that inspires you',
    'portfolio_link' => 'Portfolio Link',
    'portfolio_link_placeholder' => 'Link to your portfolio (optional)',

    'developer' => 'Developer',
    'programming_languages' => 'Programming Languages',
    'programming_languages_placeholder' => 'e.g. Java, JavaScript, Python',
    'preferred_ide' => 'Preferred IDE',
    'preferred_ide_placeholder' => 'e.g. VSCode, IntelliJ',

    'music_producer' => 'Music Producer',
    'daw' => 'DAW',
    'daw_placeholder' => 'e.g. FL Studio, Ableton',

    'desired_role' => 'Desired Role',
    'desired_role_placeholder' => 'Describe your desired role in the team (optional)',

    // Processing
    'processing' => 'Processing...',

    // Submission
    'application_submitted' => 'Application Submitted',
    'application_updated' => 'Application Updated',
    'thank_you' => 'Thank you for your application!',
    'we_will_contact' => 'We will review your application and contact you via Discord.',
    'application_not_editable' => 'This application cannot be edited at this time.',
    'edit_application' => 'Edit Application',

    // Errors
    'please_select_at_least_one_profession' => 'Please go back and select at least one profession.',
    'application_not_found_or_no_access' => 'Application not found or you do not have access to it.',
    'filtering_error' => 'Error filtering: :error',
    'created_at' => 'Created At',
    'updated_at' => 'Updated At',
    'status' => 'Status',
    'age' => 'Age',
    'age_placeholder' => 'Your age',
    'gender' => 'Gender',
    'gender_select' => 'Please select',
    'gender_male' => 'Male',
    'gender_female' => 'Female',
    'gender_diverse' => 'Diverse',
    'gender_no_info' => 'Prefer not to say',
    'confirmation' => 'Confirmation',
    'previous_step' => 'Previous',
    'next_step' => 'Next',
    'submit_application' => 'Submit Application',

    // Additional fields for application wizard
    'optional' => 'optional',
    'Berufe' => 'Professions',
    'mindestens einen auswählen' => 'select at least one',
    'Andere Berufsbezeichnung' => 'Other Profession Title',
    'Bitte spezifizieren' => 'Please specify',
    'Audio:' => 'Audio:',
    'Bitte wählen' => 'Please select',
    'Tief' => 'Deep',
    'Mittel' => 'Medium',
    'Hoch' => 'High',
    'Mikrofon' => 'Microphone',
    'Dein Mikrofon' => 'Your microphone',
    'Pc-Stats:' => 'PC Stats:',
    'Hast du einen PC?' => 'Do you have a PC?',
    'Hast du Modrinth?' => 'Do you have Modrinth?',
    'Hast du Minecraft Java?' => 'Do you have Minecraft Java?',
    'z.B. 16GB' => 'e.g. 16GB',
    'FPS in Vanilla Minecraft' => 'FPS in Vanilla Minecraft',
    '<10' => '<10',
    '10' => '10',
    '20' => '20',
    '30' => '30',
    '40' => '40',
    '50' => '50',
    '>60' => '>60',
    'Grafikkarte' => 'Graphics Card',
    'Deine Grafikkarte' => 'Your graphics card',
    'Design:' => 'Design:',
    'Design-Programme' => 'Design Programs',
    'z.B. Photoshop, Illustrator' => 'e.g. Photoshop, Illustrator',
    'Design-Stil' => 'Design Style',
    'Dein bevorzugter Design-Stil' => 'Your preferred design style',
    'Favorisiertes Design' => 'Favorite Design',
    'Ein Design, das dich inspiriert' => 'A design that inspires you',
    'Developement:' => 'Development:',
    'Programmiersprachen' => 'Programming Languages',
    'z.B. Java, JavaScript, Python' => 'e.g. Java, JavaScript, Python',
    'Bevorzugte IDE' => 'Preferred IDE',
    'z.B. VSCode, IntelliJ' => 'e.g. VSCode, IntelliJ',
    'Modelierung:' => 'Modeling:',
    '3D Model Software' => '3D Modeling Software',
    'z.B. FL Studio, Ableton' => 'e.g. FL Studio, Ableton',
    'Musikproduzierung:' => 'Music Production:',
    'Andere:' => 'Other:',
    'Andere Rolle' => 'Other Role',
    'Andere Rolle Wie Tester,...' => 'Other role like Tester,...',
    'Portfolio / Referenzen' => 'Portfolio / References',
    'Schreibe Hier Deine Referenzen die du Hast wie z.b. Links, Namen...' => 'Write your references here such as links, names...',
    'Angestrebte Rolle' => 'Desired Role',
    'Beschreibe deine Wunschrolle im Team (optional)' => 'Describe your desired role in the team (optional)',
    'Über dich' => 'About You',
    'Erzähle uns etwas über dich (mindestens 50 Zeichen)' => 'Tell us something about yourself (minimum 50 characters)',
    'Stärken & Schwächen' => 'Strengths & Weaknesses',
    'Beschreibe deine Stärken und Schwächen (mindestens 50 Zeichen)' => 'Describe your strengths and weaknesses (minimum 50 characters)',
    'Zeichen' => 'characters',
    'Abschließende Worte' => 'Final Words',
    'Möchtest du uns noch etwas mitteilen?' => 'Would you like to tell us anything else?',
    'Überprüfen & Absenden' => 'Review & Submit',
    'Persönliche Daten' => 'Personal Data',
    'Rollenspezifische Daten' => 'Role-Specific Data',
    'Nicht angegeben' => 'Not specified',
    'Ich bestätige, dass alle angegebenen Informationen korrekt sind' => 'I confirm that all information provided is correct',
    'Wird verarbeitet...' => 'Processing...',
    'Bewerbungsformular' => 'Application Form',
    'Dein Name' => 'Your name',
    'Zurück' => 'Back',
    'Weiter' => 'Next',
    'Bewerbung absenden' => 'Submit Application'
];
