<div class="application-wizard-container">
<div class="application-wizard min-h-screen bg-gradient-to-br from-base-100 via-base-100 to-base-200/30">
    <!-- Application Wizard - Modern Open Design -->
    <!-- Hero Section with Progress -->
    <div class="relative overflow-hidden">
        <!-- Background decorative elements -->
        <div class="absolute inset-0 overflow-hidden pointer-events-none">
            <div class="absolute -top-40 -right-40 w-80 h-80 bg-primary/5 rounded-full blur-3xl"></div>
            <div class="absolute top-1/2 -left-40 w-60 h-60 bg-secondary/5 rounded-full blur-2xl"></div>
        </div>

        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-12 pb-16">
            <!-- Header -->
            <div class="text-center mb-12">
                <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-primary to-secondary mb-4">
                    {{ __('application.application_form') }}
                </h1>
                <p class="text-xl text-base-content/70 max-w-2xl mx-auto">
                    {{ __('application.application_description') }}
                </p>
            </div>

            <!-- Modern Progress Indicator -->
            <div class="max-w-4xl mx-auto mb-16">
                <div class="relative">
                    <!-- Progress Line -->
                    <div class="absolute top-6 left-0 w-full h-0.5 bg-base-300"></div>
                    <div class="absolute top-6 left-0 h-0.5 bg-gradient-to-r from-primary to-secondary transition-all duration-700 ease-out"
                         style="width: {{ (($currentStep - 1) / 3) * 100 }}%"></div>

                    <!-- Step Indicators -->
                    <div class="relative flex justify-between">
                        @foreach([
                            ['step' => 1, 'title' => __('application.personal_data'), 'icon' => 'user'],
                            ['step' => 2, 'title' => __('application.role_specific'), 'icon' => 'briefcase'],
                            ['step' => 3, 'title' => __('application.about_you'), 'icon' => 'heart'],
                            ['step' => 4, 'title' => __('application.review'), 'icon' => 'check-circle']
                        ] as $stepData)
                            <div class="flex flex-col items-center group">
                                <div @class([
                                    'w-12 h-12 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-300 mb-3',
                                    'bg-gradient-to-r from-primary to-secondary text-white shadow-lg scale-110' => $currentStep >= $stepData['step'],
                                    'bg-base-200 text-base-content/60 group-hover:bg-base-300' => $currentStep < $stepData['step']
                                ])>
                                    @if($currentStep > $stepData['step'])
                                        <x-heroicon-o-check class="h-6 w-6" />
                                    @else
                                        @switch($stepData['icon'])
                                            @case('user')
                                                <x-heroicon-o-user class="h-6 w-6" />
                                                @break
                                            @case('briefcase')
                                                <x-heroicon-o-briefcase class="h-6 w-6" />
                                                @break
                                            @case('heart')
                                                <x-heroicon-o-heart class="h-6 w-6" />
                                                @break
                                            @case('check-circle')
                                                <x-heroicon-o-check-circle class="h-6 w-6" />
                                                @break
                                        @endswitch
                                    @endif
                                </div>
                                <span @class([
                                    'text-sm font-medium text-center transition-colors duration-300',
                                    'text-primary' => $currentStep == $stepData['step'],
                                    'text-base-content' => $currentStep > $stepData['step'],
                                    'text-base-content/60' => $currentStep < $stepData['step']
                                ])>
                                    {{ $stepData['title'] }}
                                </span>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
        <div class="animate-fade-in">
            <!-- Step 1: Personal Information -->
            @if($currentStep == 1)
                <div class="space-y-12 animate-slide-up">
                    <!-- Section Header -->
                    <div class="text-center">
                        <h2 class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-primary to-secondary mb-4">
                            {{ __('application.personal_data') }}
                        </h2>
                        <p class="text-lg text-base-content/70">
                            {{ __('application.personal_data_description') }}
                        </p>
                    </div>

                    <!-- GDPR Data Notice -->
                    <div class="bg-info/10 border border-info/20 rounded-2xl p-6">
                        <x-gdpr-data-notice type="application" />
                    </div>

                    <!-- Personal Information Form -->
                    <div class="space-y-8">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                            <!-- Name -->
                            <div class="space-y-2">
                                <label for="name" class="block text-sm font-semibold text-base-content/90">
                                    {{ __('application.name') }}
                                </label>
                                <x-modern-input
                                    type="text"
                                    placeholder="{{ __('application.name_placeholder') }}"
                                    variant="outlined"
                                    id="name"
                                    wire:model.live="name"
                                    class="w-full text-lg py-4"
                                />
                                @error('name')
                                    <span class="text-error text-sm flex items-center gap-2">
                                        <x-heroicon-o-exclamation-circle class="w-4 h-4" />
                                        {{ $message }}
                                    </span>
                                @enderror
                                <x-gdpr-field-explanation field="name" />
                            </div>

                            <!-- Age -->
                            <div class="space-y-2">
                                <label for="age" class="block text-sm font-semibold text-base-content/90">
                                    {{ __('application.age') }}
                                </label>
                                <x-modern-input
                                    type="number"
                                    placeholder="{{ __('application.age_placeholder') }}"
                                    variant="outlined"
                                    id="age"
                                    wire:model.live="age"
                                    min="12"
                                    max="120"
                                    class="w-full text-lg py-4"
                                />
                                @error('age')
                                    <span class="text-error text-sm flex items-center gap-2">
                                        <x-heroicon-o-exclamation-circle class="w-4 h-4" />
                                        {{ $message }}
                                    </span>
                                @enderror
                                <x-gdpr-field-explanation field="age" />
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                            <!-- Gender -->
                            <div class="space-y-2">
                                <label for="gender" class="block text-sm font-semibold text-base-content/90">
                                    {{ __('application.gender') }}
                                </label>
                                <select id="gender" wire:model.live="gender" class="select select-bordered w-full text-lg py-4 bg-base-100 border-base-300 focus:border-primary">
                                    <option value="">{{ __('application.gender_select') }}</option>
                                    <option value="male">{{ __('application.gender_male') }}</option>
                                    <option value="female">{{ __('application.gender_female') }}</option>
                                    <option value="diverse">{{ __('application.gender_diverse') }}</option>
                                    <option value="prefer_not_to_say">{{ __('application.gender_no_info') }}</option>
                                </select>
                                @error('gender')
                                    <span class="text-error text-sm flex items-center gap-2">
                                        <x-heroicon-o-exclamation-circle class="w-4 h-4" />
                                        {{ $message }}
                                    </span>
                                @enderror
                            </div>

                            <!-- Pronouns -->
                            <div class="space-y-2">
                                <label for="pronouns" class="block text-sm font-semibold text-base-content/90">
                                    {{ __('application.pronouns') }}
                                    <span class="text-base-content/50 font-normal">({{ __('application.optional') }})</span>
                                </label>
                                <x-modern-input
                                    type="text"
                                    placeholder="{{ __('application.pronouns_placeholder') }}"
                                    variant="outlined"
                                    id="pronouns"
                                    wire:model.live="pronouns"
                                    class="w-full text-lg py-4"
                                />
                            </div>
                        </div>
                    </div>

                    <!-- Professions -->
                    <div>
                        <h3 class="text-lg font-semibold mb-4 text-center">{{ __('application.professions_question') }}</h3>
                        <p class="text-base-content/70 mb-6 text-center">{{ __('application.professions_select_description') }}</p>

                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                            @php
                                $professionIcons = [
                                    'actor' => ['gradient' => 'from-purple-500 to-pink-500', 'icon' => 'user'],
                                    'actor_no_voice' => ['gradient' => 'from-blue-500 to-cyan-500', 'icon' => 'eye'],
                                    'voice_actor' => ['gradient' => 'from-green-500 to-emerald-500', 'icon' => 'microphone'],
                                    'builder' => ['gradient' => 'from-orange-500 to-red-500', 'icon' => 'cog'],
                                    'designer' => ['gradient' => 'from-indigo-500 to-purple-500', 'icon' => 'pencil'],
                                    'developer' => ['gradient' => 'from-yellow-500 to-orange-500', 'icon' => 'code'],
                                    'cutter' => ['gradient' => 'from-pink-500 to-rose-500', 'icon' => 'film'],
                                    'cameraman' => ['gradient' => 'from-teal-500 to-cyan-500', 'icon' => 'camera'],
                                    'modeler' => ['gradient' => 'from-violet-500 to-purple-500', 'icon' => 'cube'],
                                    'music_producer' => ['gradient' => 'from-emerald-500 to-teal-500', 'icon' => 'musical-note'],
                                    'other' => ['gradient' => 'from-gray-500 to-slate-500', 'icon' => 'ellipsis-horizontal']
                                ];
                            @endphp

                            @foreach($professionOptions as $value => $label)
                                @php
                                    $config = $professionIcons[$value] ?? $professionIcons['other'];
                                    $isSelected = in_array($value, $professions ?? []);
                                @endphp
                                <div class="profession-card p-4 border-2 border-base-300 rounded-xl cursor-pointer bg-base-200/30 hover:bg-base-200/50 transition-all duration-300 hover:transform hover:-translate-y-0.5 hover:shadow-lg @if($isSelected) selected border-primary bg-gradient-to-br from-primary/20 to-secondary/20 @endif"
                                     wire:click="toggleProfession('{{ $value }}')">
                                    <div class="flex items-center justify-between mb-3">
                                        <div class="flex items-center">
                                            <div class="w-10 h-10 bg-gradient-to-br {{ $config['gradient'] }} rounded-lg flex items-center justify-center mr-3">
                                                @switch($config['icon'])
                                                    @case('user')
                                                        <x-heroicon-o-user class="w-5 h-5 text-white" />
                                                        @break
                                                    @case('eye')
                                                        <x-heroicon-o-eye class="w-5 h-5 text-white" />
                                                        @break
                                                    @case('microphone')
                                                        <x-heroicon-o-microphone class="w-5 h-5 text-white" />
                                                        @break
                                                    @case('cog')
                                                        <x-heroicon-o-cog-6-tooth class="w-5 h-5 text-white" />
                                                        @break
                                                    @case('pencil')
                                                        <x-heroicon-o-pencil class="w-5 h-5 text-white" />
                                                        @break
                                                    @case('code')
                                                        <x-heroicon-o-code-bracket class="w-5 h-5 text-white" />
                                                        @break
                                                    @case('film')
                                                        <x-heroicon-o-film class="w-5 h-5 text-white" />
                                                        @break
                                                    @case('camera')
                                                        <x-heroicon-o-camera class="w-5 h-5 text-white" />
                                                        @break
                                                    @case('cube')
                                                        <x-heroicon-o-cube class="w-5 h-5 text-white" />
                                                        @break
                                                    @case('musical-note')
                                                        <x-heroicon-o-musical-note class="w-5 h-5 text-white" />
                                                        @break
                                                    @default
                                                        <x-heroicon-o-ellipsis-horizontal class="w-5 h-5 text-white" />
                                                @endswitch
                                            </div>
                                            <div>
                                                <h4 class="font-semibold">{{ $label }}</h4>
                                                <p class="text-xs text-base-content/70">{{ __('application.profession_subtitle.' . $value) }}</p>
                                            </div>
                                        </div>
                                        <div class="checkbox-indicator w-5 h-5 border-2 border-base-300 rounded flex items-center justify-center transition-all duration-300 @if($isSelected) bg-primary border-primary @endif">
                                            @if($isSelected)
                                                <x-heroicon-o-check class="w-3 h-3 text-white" />
                                            @endif
                                        </div>
                                    </div>
                                    <p class="text-sm text-base-content/80">{{ __('application.profession_description_long.' . $value) }}</p>
                                </div>
                            @endforeach
                        </div>
                        @error('professions')
                        <span class="text-error text-sm block mt-4">{{ $message }}</span>
                        @enderror
                        @if(session()->has('professions_error'))
                            <span class="text-error text-sm block mt-4">{{ session('professions_error') }}</span>
                        @endif
                    </div>

                    <!-- Other Profession -->
                    @if(in_array('other', $professions ?? []))
                        <div class="animate-fade-in">
                            <label for="otherProfession" class="block text-sm font-medium">{{ __('application.other_profession') }}</label>
                            <x-modern-input type="text" placeholder="{{ __('application.other_profession_placeholder') }}" variant="outlined" id="otherProfession" wire:model.live="otherProfession" />
                            @error('otherProfession') <span class="text-error text-sm">{{ $message }}</span> @enderror
                        </div>
                    @endif
                </div>
            @endif

            <!-- Step 2: Role-specific Information -->
            @if($currentStep == 2)
                <div class="space-y-6 animate-slide-up">
                    <h2 class="text-xl font-semibold mb-4">{{ __('application.role_specific') }}</h2>

                    <!-- Notice if no professions selected -->
                    @if(empty($professions))
                        <div class="alert alert-warning">
                            <x-heroicon-o-exclamation-triangle class="h-6 w-6" />
                            <span>{{ __('application.please_select_at_least_one_profession') }}</span>
                        </div>
                    @endif

                    <!-- Different fields based on selected professions -->
                    @if(in_array('actor', $professions ?? []) || in_array('voice_actor', $professions ?? []))
                        <div class="card bg-base-300/50 p-4 mb-6 animate-fade-in">
                            <h3 class="font-medium mb-3">{{ __('application.acting_voice_acting') }}:</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="voice_type" class="block text-sm font-medium">{{ __('application.voice_type') }}</label>
                                    <select id="voice_type" wire:model.live="voice_type" class="mt-1 select select-bordered w-full">
                                        <option value="">{{ __('application.gender_select') }}</option>
                                        <option value="deep">{{ __('application.voice_type_deep') }}</option>
                                        <option value="medium">{{ __('application.voice_type_medium') }}</option>
                                        <option value="high">{{ __('application.voice_type_high') }}</option>
                                    </select>
                                </div>
                                <div>
                                    <label for="microphone" class="block text-sm font-medium">{{ __('application.microphone') }}</label>
                                    <x-modern-input type="text" placeholder="{{ __('application.microphone_placeholder') }}" variant="outlined" id="microphone" wire:model.live="microphone" />
                                </div>
                            </div>
                        </div>
                    @endif

                    @php
                        $pcRelevantProfessions = ['builder', 'cameraman', 'actor', 'actor_no_voice', 'designer', 'voice_actor', 'cutter', 'developer'];
                        $minecraftRelevantProfessions = ['builder', 'cameraman', 'actor', 'actor_no_voice'];
                        $gpuRelevantProfessions = ['builder', 'cameraman', 'actor', 'actor_no_voice', 'cutter'];
                    @endphp

                    @if(array_intersect($pcRelevantProfessions, $professions ?? []))
                        <div class="card bg-base-300/50 p-4 mb-6 animate-fade-in">
                            <h3 class="font-medium mb-3">{{ __('application.builder_cameraman') }}:</h3>

                            <!-- PC Checkbox -->
                            <div class="mb-3">
                                <div class="relative">
                                    <input type="checkbox" id="has_pc" wire:model="has_pc" class="peer sr-only">
                                    <label for="has_pc" class="flex items-center p-3 bg-white border-2 border-gray-200 rounded-lg cursor-pointer
               peer-checked:border-blue-600 peer-checked:text-blue-600 peer-checked:bg-blue-50 hover:text-gray-600 hover:bg-gray-50
               dark:hover:text-gray-300 dark:border-gray-700 dark:peer-checked:text-blue-500 dark:peer-checked:bg-blue-900/20
               dark:text-gray-400 dark:bg-gray-800 dark:hover:bg-gray-700">
                                        <span class="text-sm font-medium">{{ __('application.Hast du einen PC?') }}</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Minecraft-related fields -->
                            @if(array_intersect($minecraftRelevantProfessions, $professions ?? []))
                                <!-- Modrinth Checkbox -->
                                <div class="mb-3">
                                    <div class="relative">
                                        <input type="checkbox" id="has_curseforge" wire:model="has_curseforge" class="peer sr-only">
                                        <label for="has_curseforge" class="flex items-center p-3 bg-white border-2 border-gray-200 rounded-lg cursor-pointer
                           peer-checked:border-blue-600 peer-checked:text-blue-600 hover:text-gray-600 hover:bg-gray-50
                           dark:hover:text-gray-300 dark:border-gray-700 dark:peer-checked:text-blue-500
                           dark:text-gray-400 dark:bg-gray-800 dark:hover:bg-gray-700">
                                            <span class="text-sm font-medium">{{ __('application.Hast du Modrinth?') }}</span>
                                        </label>
                                    </div>
                                </div>

                                <!-- Minecraft Java Checkbox -->
                                <div class="mb-3">
                                    <div class="relative">
                                        <input type="checkbox" id="has_minecraft_java" wire:model="has_minecraft_java" class="peer sr-only">
                                        <label for="has_minecraft_java" class="flex items-center p-3 bg-white border-2 border-gray-200 rounded-lg cursor-pointer
                           peer-checked:border-blue-600 peer-checked:text-blue-600 hover:text-gray-600 hover:bg-gray-50
                           dark:hover:text-gray-300 dark:border-gray-700 dark:peer-checked:text-blue-500
                           dark:text-gray-400 dark:bg-gray-800 dark:hover:bg-gray-700">
                                            <span class="text-sm font-medium">{{ __('application.Hast du Minecraft Java?') }}</span>
                                        </label>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label for="ram" class="block text-sm font-medium">{{ __('application.ram') }}</label>
                                        <x-modern-input type="text" placeholder="{{ __('application.ram_placeholder') }}" variant="outlined" id="ram" wire:model.live="ram" />
                                        <x-gdpr-field-explanation field="ram" />
                                    </div>
                                    <div>
                                        <label for="fps" class="block text-sm font-medium">{{ __('application.fps') }}</label>
                                        <select id="fps" wire:model.live="fps" class="mt-1 select select-bordered w-full">
                                            <option value="">{{ __('application.gender_select') }}</option>
                                            <option value="<10">{{ __('application.<10') }}</option>
                                            <option value="10-">{{ __('application.10') }}</option>
                                            <option value="20">{{ __('application.20') }}</option>
                                            <option value="30">{{ __('application.30') }}</option>
                                            <option value="40">{{ __('application.40') }}</option>
                                            <option value="50">{{ __('application.50') }}</option>
                                            <option value="60<">{{ __('application.>60') }}</option>
                                        </select>
                                    </div>
                                </div>
                            @endif

                            <!-- GPU-related field -->
                            @if(array_intersect($gpuRelevantProfessions, $professions ?? []))
                                <div>
                                    <label for="gpu" class="block text-sm font-medium">{{ __('application.gpu') }}</label>
                                    <x-modern-input type="text" placeholder="{{ __('application.gpu_placeholder') }}" variant="outlined" id="gpu" wire:model.live="gpu" />
                                    <x-gdpr-field-explanation field="gpu" />
                                </div>
                            @endif
                        </div>
                    @endif

                    @if(in_array('designer', $professions ?? []))
                        <div class="card bg-base-300/50 p-4 mb-6 animate-fade-in">
                            <h3 class="font-medium mb-3">{{ __('application.designer') }}:</h3>
                            <div class="grid grid-cols-1 gap-4">
                                <div>
                                    <label for="program" class="block text-sm font-medium">{{ __('application.design_programs') }}</label>
                                    <x-modern-input type="text" placeholder="{{ __('application.design_programs_placeholder') }}" variant="outlined" id="program" wire:model.live="program" />
                                </div>
                                <div>
                                    <label for="design_style" class="block text-sm font-medium">{{ __('application.design_style') }}</label>
                                    <x-modern-input type="text" placeholder="{{ __('application.design_style_placeholder') }}" variant="outlined" id="design_style" wire:model.live="design_style" />
                                </div>
                                <div>
                                    <label for="favorite_design" class="block text-sm font-medium">{{ __('application.favorite_design') }}</label>
                                    <x-modern-input type="text" placeholder="{{ __('application.favorite_design_placeholder') }}" variant="outlined" id="favorite_design" wire:model.live="favorite_design" />
                                </div>
                            </div>
                        </div>
                    @endif

                    @if(in_array('developer', $professions ?? []))
                        <div class="card bg-base-300/50 p-4 mb-6 animate-fade-in">
                            <h3 class="font-medium mb-3">{{ __('application.developer') }}:</h3>
                            <div class="grid grid-cols-1 gap-4">
                                <div>
                                    <label for="languages" class="block text-sm font-medium">{{ __('application.programming_languages') }}</label>
                                    <x-modern-input type="text" placeholder="{{ __('application.programming_languages_placeholder') }}" variant="outlined" id="languages" wire:model.live="languages" />
                                </div>
                                <div>
                                    <label for="ide" class="block text-sm font-medium">{{ __('application.preferred_ide') }}</label>
                                    <x-modern-input type="text" placeholder="{{ __('application.preferred_ide_placeholder') }}" variant="outlined" id="ide" wire:model.live="ide" />
                                </div>
                            </div>
                        </div>
                    @endif

                    @if(in_array('modeler', $professions ?? []))
                        <div class="card bg-base-300/50 p-4 mb-6 animate-fade-in">
                            <h3 class="font-medium mb-3">{{ __('application.modeler') }}:</h3>
                            <div class="grid grid-cols-1 gap-4">
                                <div>
                                    <label for="design_program" class="block text-sm font-medium">{{ __('application.3D Model Software') }}</label>
                                    <x-modern-input type="text" placeholder="{{ __('application.daw_placeholder') }}" variant="outlined" id="daw" wire:model.live="daw" />
                                </div>
                            </div>
                        </div>
                    @endif

                    @if(in_array('music_producer', $professions ?? []))
                        <div class="card bg-base-300/50 p-4 mb-6 animate-fade-in">
                            <h3 class="font-medium mb-3">{{ __('application.music_producer') }}:</h3>
                            <div class="grid grid-cols-1 gap-4">
                                <div>
                                    <label for="daw" class="block text-sm font-medium">{{ __('application.daw') }}</label>
                                    <x-modern-input type="text" placeholder="{{ __('application.daw_placeholder') }}" variant="outlined" id="daw" wire:model.live="daw" />
                                </div>
                            </div>
                        </div>
                    @endif

                    @if(in_array('other', $professions ?? []))
                        <div class="card bg-base-300/50 p-4 mb-6 animate-fade-in">
                            <h3 class="font-medium mb-3">{{ __('application.other_profession') }}:</h3>
                            <div class="grid grid-cols-1 gap-4">
                                <div>
                                    <label for="otherProfession" class="block text-sm font-medium">{{ __('application.other_profession') }}</label>
                                    <x-modern-input type="text" placeholder="{{ __('application.other_profession_placeholder') }}" variant="outlined" id="otherProfession" wire:model.live="otherProfession" />
                                </div>
                            </div>
                        </div>
                    @endif

                    <div>
                        <label for="portfolio" class="block text-sm font-medium">{{ __('application.portfolio') }}</label>
                        <textarea id="portfolio" wire:model.live="portfolio" rows="3" class="mt-1 textarea textarea-bordered w-full" placeholder="{{ __('application.portfolio_placeholder') }}"></textarea>
                    </div>
                    <div>
                        <label for="desired_role" class="block text-sm font-medium">{{ __('application.desired_role') }}</label>
                        <textarea id="desired_role" wire:model.live="desired_role" rows="3" class="mt-1 textarea textarea-bordered w-full" placeholder="{{ __('application.desired_role_placeholder') }}"></textarea>
                    </div>
                </div>
            @endif

            <!-- Step 3: About You -->
            @if($currentStep == 3)
                <div class="space-y-6 animate-slide-up">
                    <h2 class="text-xl font-semibold mb-4">{{ __('application.about_you') }}</h2>

                    <div>
                        <label for="about_you" class="block text-sm font-medium">{{ __('application.about_you') }}</label>
                        <textarea id="about_you" wire:model.live="about_you" rows="5" class="mt-1 textarea textarea-bordered w-full" placeholder="{{ __('application.about_you_placeholder') }}"></textarea>
                        @error('about_you') <span class="text-error text-sm">{{ $message }}</span> @enderror
                        <div class="text-xs text-right mt-1">{{ strlen($about_you ?? '') }}/50+ {{ __('application.characters') }}</div>
                    </div>

                    <div>
                        <label for="strengths_weaknesses" class="block text-sm font-medium">{{ __('application.strengths_weaknesses') }}</label>
                        <textarea id="strengths_weaknesses" wire:model.live="strengths_weaknesses" rows="5" class="mt-1 textarea textarea-bordered w-full" placeholder="{{ __('application.strengths_weaknesses_placeholder') }}"></textarea>
                        @error('strengths_weaknesses') <span class="text-error text-sm">{{ $message }}</span> @enderror
                        <div class="text-xs text-right mt-1">{{ strlen($strengths_weaknesses ?? '') }}/50+ {{ __('application.characters') }}</div>
                    </div>

                    <div>
                        <label for="final_words" class="block text-sm font-medium">{{ __('application.final_words') }} ({{ __('application.optional') }})</label>
                        <textarea id="final_words" wire:model.live="final_words" rows="3" class="mt-1 textarea textarea-bordered w-full" placeholder="{{ __('application.final_words_placeholder') }}"></textarea>
                    </div>
                </div>
            @endif

            <!-- Step 4: Review -->
            @if($currentStep == 4)
                <div class="space-y-6 animate-slide-up">
                    <h2 class="text-xl font-semibold mb-4">{{ __('application.review_submit') }}</h2>

                    <div class="space-y-4">
                        <!-- Personal Information Review -->
                        <div class="collapse collapse-arrow bg-base-300/50">
                            <input type="checkbox" checked />
                            <div class="collapse-title font-medium">
                                {{ __('application.personal_data') }}
                            </div>
                            <div class="collapse-content">
                                <dl class="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-2 text-sm">
                                    <div>
                                        <dt class="font-medium">{{ __('application.name') }}:</dt>
                                        <dd>{{ $name }}</dd>
                                    </div>
                                    <div>
                                        <dt class="font-medium">{{ __('application.age') }}:</dt>
                                        <dd>{{ $age }}</dd>
                                    </div>
                                    <div>
                                        <dt class="font-medium">{{ __('application.gender') }}:</dt>
                                        <dd>{{ match($gender) {
                                        'male' => __('application.gender_male'),
                                        'female' => __('application.gender_female'),
                                        'diverse' => __('application.gender_diverse'),
                                        'prefer_not_to_say' => __('application.gender_no_info'),
                                        default => $gender
                                    } }}</dd>
                                    </div>
                                    <div>
                                        <dt class="font-medium">{{ __('application.pronouns') }}:</dt>
                                        <dd>{{ $pronouns ?: __('application.not_specified') }}</dd>
                                    </div>
                                    <div class="sm:col-span-2">
                                        <dt class="font-medium">{{ __('application.professions') }}:</dt>
                                        <dd>
                                            <div class="flex flex-wrap gap-1 mt-1">
                                                @foreach($professions ?? [] as $profession)
                                                    @if($profession !== 'other')
                                                        <span class="badge badge-primary">{{ $professionOptions[$profession] ?? $profession }}</span>
                                                    @endif
                                                @endforeach

                                                @if(in_array('other', $professions ?? []) && $otherProfession)
                                                    <span class="badge badge-secondary">{{ $otherProfession }}</span>
                                                @endif
                                            </div>
                                        </dd>
                                    </div>
                                </dl>
                            </div>
                        </div>


                        <!-- Role-specific Information Review -->
                        <div class="collapse collapse-arrow bg-base-300/50">
                            <input type="checkbox" checked />
                            <div class="collapse-title font-medium">
                                {{ __('application.role_specific') }}
                            </div>
                            <div class="collapse-content">
                                <dl class="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-2 text-sm">
                                    @if(!empty($checkboxQuestions))
                                        <div class="collapse collapse-arrow bg-base-300/50">
                                            <input type="checkbox" checked />
                                            <div class="collapse-title font-medium">
                                                {{ __('application.checkbox_questions') }}
                                            </div>
                                            <div class="collapse-content">
                                                <ul class="list-disc list-inside">
                                                    @foreach($checkboxQuestions as $question => $answer)
                                                        <li>
                                                            <strong>{{ $question }}:</strong>
                                                            {{ $answer ? __('application.yes') : __('application.no') }}
                                                        </li>
                                                    @endforeach
                                                </ul>
                                            </div>
                                        </div>
                                    @endif

                                    @if($voice_type)
                                        <div>
                                            <dt class="font-medium">{{ __('application.voice_type') }}:</dt>
                                            <dd>{{ match($voice_type) {
                                        'deep' => __('application.voice_type_deep'),
                                        'medium' => __('application.voice_type_medium'),
                                        'high' => __('application.voice_type_high'),
                                        default => $voice_type
                                    } }}</dd>
                                        </div>
                                    @endif

                                    @if($microphone)
                                        <div>
                                            <dt class="font-medium">{{ __('application.microphone') }}:</dt>
                                            <dd>{{ $microphone }}</dd>
                                        </div>
                                    @endif

                                    @if($ram)
                                        <div>
                                            <dt class="font-medium">{{ __('application.ram') }}:</dt>
                                            <dd>{{ $ram }}</dd>
                                        </div>
                                    @endif

                                    @if($fps)
                                        <div>
                                            <dt class="font-medium">{{ __('application.fps') }}:</dt>
                                            <dd>{{ $fps }}</dd>
                                        </div>
                                    @endif

                                    @if($gpu)
                                        <div>
                                            <dt class="font-medium">{{ __('application.gpu') }}:</dt>
                                            <dd>{{ $gpu }}</dd>
                                        </div>
                                    @endif

                                    @if($program)
                                        <div>
                                            <dt class="font-medium">{{ __('application.design_programs') }}:</dt>
                                            <dd>{{ $program }}</dd>
                                        </div>
                                    @endif

                                    @if($design_style)
                                        <div>
                                            <dt class="font-medium">{{ __('application.design_style') }}:</dt>
                                            <dd>{{ $design_style }}</dd>
                                        </div>
                                    @endif

                                    @if($favorite_design)
                                        <div>
                                            <dt class="font-medium">{{ __('application.favorite_design') }}:</dt>
                                            <dd>{{ $favorite_design }}</dd>
                                        </div>
                                    @endif

                                    @if($portfolio)
                                        <div>
                                            <dt class="font-medium">{{ __('application.portfolio') }}:</dt>
                                            <dd><a href="{{ $portfolio }}" class="link link-primary" target="_blank">{{ $portfolio }}</a></dd>
                                        </div>
                                    @endif

                                    @if($languages)
                                        <div>
                                            <dt class="font-medium">{{ __('application.programming_languages') }}:</dt>
                                            <dd>{{ $languages }}</dd>
                                        </div>
                                    @endif

                                    @if($ide)
                                        <div>
                                            <dt class="font-medium">{{ __('application.preferred_ide') }}:</dt>
                                            <dd>{{ $ide }}</dd>
                                        </div>
                                    @endif

                                    @if($daw)
                                        <div>
                                            <dt class="font-medium">{{ __('application.daw') }}:</dt>
                                            <dd>{{ $daw }}</dd>
                                        </div>
                                    @endif

                                    @if($desired_role)
                                        <div class="sm:col-span-2">
                                            <dt class="font-medium">{{ __('application.desired_role') }}:</dt>
                                            <dd class="whitespace-pre-line">{{ $desired_role }}</dd>
                                        </div>
                                    @endif
                                </dl>
                            </div>
                        </div>

                        <!-- About You Review -->
                        <div class="collapse collapse-arrow bg-base-300/50">
                            <input type="checkbox" checked />
                            <div class="collapse-title font-medium">
                                {{ __('application.about_you') }}
                            </div>
                            <div class="collapse-content">
                                <dl class="grid grid-cols-1 gap-y-4 text-sm">
                                    <div>
                                        <dt class="font-medium">{{ __('application.about_you') }}:</dt>
                                        <dd class="mt-1 whitespace-pre-line">{{ $about_you }}</dd>
                                    </div>

                                    <div>
                                        <dt class="font-medium">{{ __('application.strengths_weaknesses') }}:</dt>
                                        <dd class="mt-1 whitespace-pre-line">{{ $strengths_weaknesses }}</dd>
                                    </div>

                                    @if($final_words)
                                        <div>
                                            <dt class="font-medium">{{ __('application.final_words') }}:</dt>
                                            <dd class="mt-1 whitespace-pre-line">{{ $final_words }}</dd>
                                        </div>
                                    @endif
                                </dl>
                            </div>
                        </div>

                        <!-- Confirmation -->
                        <div class="mt-6 flex items-start">
                            <div class="flex h-5 items-center">
                                <input id="confirmation" wire:model.live="confirmation" type="checkbox" class="checkbox checkbox-primary">
                            </div>
                            <div class="ml-3 text-sm">
                                <label for="confirmation" class="font-medium">
                                    {{ __('application.confirm_correct_information') }}
                                </label>
                                @error('confirmation') <span class="text-error text-sm block">{{ $message }}</span> @enderror
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>

        </div>
    </div>
</div>

    <!-- Modern Navigation Footer -->
    <div class="fixed bottom-0 left-0 right-0 bg-base-100/95 backdrop-blur-lg border-t border-base-300/50 z-50">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex @if($currentStep > 1) justify-between @else justify-end @endif items-center">
                @if($currentStep > 1)
                    <button type="button" wire:click="previousStep"
                            class="flex items-center gap-3 px-6 py-3 text-base-content/70 hover:text-base-content transition-colors duration-200 group">
                        <div class="w-10 h-10 rounded-full bg-base-200 group-hover:bg-base-300 flex items-center justify-center transition-colors duration-200">
                            <x-heroicon-o-arrow-left class="h-5 w-5" />
                        </div>
                        <span class="font-medium">{{ __('application.previous_step') }}</span>
                    </button>
                @endif

                <div class="flex items-center gap-4">
                    @if($currentStep < 4)
                        <button type="button" wire:click="nextStep"
                                class="flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-primary to-secondary text-white rounded-2xl font-semibold text-lg hover:shadow-lg hover:scale-105 transition-all duration-200 group">
                            <span>{{ __('application.next_step') }}</span>
                            <div class="w-6 h-6 rounded-full bg-white/20 group-hover:bg-white/30 flex items-center justify-center transition-colors duration-200">
                                <x-heroicon-o-arrow-right class="h-4 w-4" />
                            </div>
                        </button>
                    @endif

                    @if($currentStep == 4)
                        <button type="button" wire:click="submitApplication"
                                class="flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-success to-emerald-600 text-white rounded-2xl font-semibold text-lg hover:shadow-lg hover:scale-105 transition-all duration-200 group">
                            <span>{{ __('application.submit_application') }}</span>
                            <div class="w-6 h-6 rounded-full bg-white/20 group-hover:bg-white/30 flex items-center justify-center transition-colors duration-200">
                                <x-heroicon-o-check-circle class="h-4 w-4" />
                            </div>
                        </button>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom padding to account for fixed footer -->
    <div class="h-24"></div>

    <!-- Loading indicator -->
    <div wire:loading wire:target="nextStep, previousStep, submitApplication" class="fixed inset-0 flex items-center justify-center bg-base-100/75 z-50">
        <div class="text-center">
            <div class="w-16 h-16 border-4 border-primary/30 border-t-primary rounded-full animate-spin mx-auto mb-4"></div>
            <p class="text-lg font-medium text-base-content">{{ __('application.processing') }}</p>
        </div>
    </div>
</div>
