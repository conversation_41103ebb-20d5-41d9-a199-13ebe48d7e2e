<div>
    <div class="bg-gradient-modern min-h-screen py-8 px-4">
        <x-modern-card variant="elevated" size="md">
            <div class="flex items-center gap-2 mb-6">
                <a href="{{ route('tickets.index') }}" class="bg-slate-700 hover:bg-slate-600 text-white rounded-full p-2 transition">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                </a>
                <h1 class="text-2xl font-bold text-base-content">
                    {{ __('tickets.create_ticket') }}
                </h1>
            </x-modern-card>

            <form wire:submit.prevent="submit">
                <div class="mb-4">
                    <label for="title" class="block text-sm font-medium text-slate-300 mb-1">{{ __('tickets.title') }}</label>
                    <input wire:model="title" type="text" id="title" class="bg-slate-800 text-slate-100 rounded-2xl px-4 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="{{ __('tickets.title_placeholder') }}">
                    @error('title') <span class="text-red-400 text-sm">{{ $message }}</span> @enderror
                </div>

                <div class="mb-6">
                    <label for="description" class="block text-sm font-medium text-slate-300 mb-1">{{ __('tickets.description') }}</label>
                    <textarea wire:model="description" id="description" rows="6" class="bg-slate-800 text-slate-100 rounded-2xl px-4 py-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="{{ __('tickets.description_placeholder') }}"></textarea>
                    @error('description') <span class="text-red-400 text-sm">{{ $message }}</span> @enderror
                </div>

                <div class="flex justify-end gap-2">
                    <a href="{{ route('tickets.index') }}" class="bg-slate-700 hover:bg-slate-600 text-white rounded-full px-4 py-2 shadow-lg transition">
                        {{ __('tickets.cancel') }}
                    </a>
                    <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white rounded-full px-4 py-2 shadow-lg transition">
                        {{ __('tickets.submit') }}
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- CSS for glass effect -->
    <style>
        .glass {
            background: rgba(30, 41, 59, 0.6);
            backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
        }
    </style>
</div>
