<x-app-layout>
    <x-slot name="heading">
        {{ __('messages.minewache_title') }}
    </x-slot>
    <x-slot name="meta">
        <meta name="description" content="{{ __('messages.meta_description', ['default' => 'Minewache – Minecraft Roleplay, Community, Bewerbungen und mehr. Werde Teil des Teams!']) }}">
        <script type="application/ld+json">
        {
          "@context": "https://schema.org",
          "@type": "Organization",
          "name": "Minewache",
          "url": "{{ url('/') }}",
          "logo": "{{ asset('images/logo.svg') }}",
          "sameAs": [
            "https://www.youtube.com/@Die-Minewache",
            "https://discord.gg/wwrK2csZnX"
          ]
        }
        </script>
    </x-slot>

    <!-- Enhanced Theme Switcher -->
    <x-enhanced-theme-switcher position="bottom-right" type="advanced" />

    <main class="flex flex-col min-h-dvh">
        <!-- Bisect Hosting Advertisement Banner -->
        <section class="bg-base-200 py-4 border-b border-base-300"
                 x-data="{
                    showAd: localStorage.getItem('bisectAdDismissed') !== 'true',
                    reduceMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches,
                    isDesktop: window.innerWidth >= 768
                 }"
                 x-show="showAd"
                 x-transition.opacity.duration.400ms
                 aria-label="Bisect Hosting Werbung">
            <div class="container mx-auto px-4">
                <x-modern-card variant="default" size="sm" class="relative flex items-center gap-4">
                    <button @click="showAd = false; localStorage.setItem('bisectAdDismissed', 'true');"
                            class="absolute top-2 right-2 z-20 btn btn-circle btn-xs bg-base-100/20 hover:bg-base-100/40 border-0 focus:outline-none focus:ring-2 focus:ring-primary"
                            aria-label="Werbung ausblenden">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <title>Schließen</title>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                    <div class="flex-shrink-0 flex items-center justify-center w-16 h-16 rounded-lg bg-primary/10 animate-fade-in">
                        <img src="/images/bisect-logo.svg" alt="Bisect Hosting Logo" class="w-12 h-12 object-contain" loading="lazy" />
                    </div>
                    <div class="flex-1 text-base-content">
                        <span class="font-semibold text-primary">Bisect Hosting</span> –
                        {{ __('messages.bisect_ad_text', ['code' => 'MINEWACHE']) }}
                    </div>
                    <x-modern-button variant="primary" href="https://bisecthosting.com/minewache" target="_blank" rel="noopener sponsored">
                        {{ __('messages.bisect_ad_cta', ['code' => 'MINEWACHE']) }}
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </x-modern-button>
                </x-modern-card>
            </div>
        </section>

        <!-- Hero Section with immersive 3D parallax effect -->
        <section class="relative overflow-hidden min-h-[90vh] flex items-center">
            <!-- Animated background particles -->
            <div class="absolute inset-0 z-0">
                <div id="particles-js" class="absolute inset-0"></div>
            </div>

            <!-- Hero content with glass card and 3D tilt -->
            <div class="container mx-auto px-4 relative z-10">
                <div class="grid grid-cols-1 lg:grid-cols-5 gap-12 items-center">
                    <!-- Animated text content with gradient -->
                    <div class="lg:col-span-3 space-y-8">
                        <div class="relative">
                            <div class="absolute -left-8 -top-12 w-64 h-64 bg-primary/10 rounded-full blur-3xl"></div>
                            <div class="absolute right-12 bottom-0 w-40 h-40 bg-secondary/10 rounded-full blur-2xl"></div>

                            <h1 class="font-display text-transparent bg-clip-text bg-gradient-to-r from-primary to-secondary text-5xl md:text-6xl lg:text-7xl font-bold tracking-tight mb-6 animate-slide-right">
                                {{ __('messages.minewache_heading') }}
                            </h1>

                            <p class="font-modern text-xl md:text-2xl text-base-content/90 mb-6 max-w-2xl animate-delayed-fade">
                                {{ __('messages.welcome_description') }}
                            </p>

                            <div class="flex flex-wrap gap-4 mt-8 animate-delayed-fade">
                                <x-modern-button variant="primary" href="{{ route('bewerben') }}" >{{ __('messages.apply_now') }}
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 animate-bounce" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                                    </svg></x-modern-button>

                                <x-modern-button variant="primary" href="{{ route('discord') }}" ><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5" aria-hidden="true">
                                        <path d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-1.25.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028c.462-.63.874-1.295 1.226-1.994a.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.419 0 1.334-.956 2.419-2.157 2.419zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.419 0 1.334-.946 2.419-2.157 2.419z"/>
                                    </svg>
                                    Discord beitreten</x-modern-button>
                            </div>

                            <!-- Floating stats -->
                            <div class="mt-12 grid grid-cols-2 md:grid-cols-4 gap-4">
                                <x-modern-card variant="default" size="sm" style="--float-delay: 0.1s">
                                    <div class="text-3xl font-bold text-primary">200+</div>
                                    <div class="text-sm text-base-content/70">Mitglieder</div>
                                </x-modern-card>
                                <x-modern-card variant="default" size="sm" style="--float-delay: 0.1s">
                                    <div class="text-3xl font-bold text-primary">30+</div>
                                    <div class="text-sm text-base-content/70">Angenommen</div>
                                </x-modern-card>
                                <x-modern-card variant="default" size="sm" style="--float-delay: 0.3s">
                                    <div class="text-3xl font-bold text-primary">50+</div>
                                    <div class="text-sm text-base-content/70">Episoden</div>
                                </x-modern-card>
                                <x-modern-card variant="default" size="sm" style="--float-delay: 0.5s">
                                    <div class="text-3xl font-bold text-primary">4+</div>
                                    <div class="text-sm text-base-content/70">Jahre aktiv</div>
                                </x-modern-card>
                            </div>
                        </div>
                    </div>

                    <!-- 3D logo with interactive effect -->
                    <div class="lg:col-span-2 relative flex justify-center">
                        <div class="absolute w-full h-full bg-gradient-to-tr from-primary/10 to-secondary/10 rounded-full blur-3xl transform scale-75 opacity-70"></div>
                        <div class="relative z-10 animate-float"
                             x-data="{
                                rotateX: 0,
                                rotateY: 0,
                                rotateZ: 0,
                                pointerMoveHandler: null,
                                pointerLeaveHandler: null,
                                initTilt() {
                                    if (window.innerWidth < 1024 || window.matchMedia('(prefers-reduced-motion: reduce)').matches) return;
                                    this.pointerMoveHandler = (e) => {
                                        const rect = this.$refs.tiltLogo.getBoundingClientRect();
                                        const x = e.clientX - rect.left - rect.width/2;
                                        const y = e.clientY - rect.top - rect.height/2;
                                        this.rotateY = x/15;
                                        this.rotateX = -y/15;
                                        this.rotateZ = this.rotateY/3;
                                    };
                                    this.pointerLeaveHandler = () => {
                                        this.rotateX = 0;
                                        this.rotateY = 0;
                                        this.rotateZ = 0;
                                    };
                                    this.$refs.tiltLogo.addEventListener('pointermove', this.pointerMoveHandler);
                                    this.$refs.tiltLogo.addEventListener('pointerleave', this.pointerLeaveHandler);
                                },
                                destroyTilt() {
                                    if (this.pointerMoveHandler && this.pointerLeaveHandler && this.$refs.tiltLogo) {
                                        this.$refs.tiltLogo.removeEventListener('pointermove', this.pointerMoveHandler);
                                        this.$refs.tiltLogo.removeEventListener('pointerleave', this.pointerLeaveHandler);
                                    }
                                }
                             }"
                             x-init="initTilt()"
                             x-ref="tiltLogo"
                             x-effect="() => { if (window.innerWidth < 1024 || window.matchMedia('(prefers-reduced-motion: reduce)').matches) destroyTilt(); }"
                             :style="`transform: perspective(2000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) rotateZ(${rotateZ}deg)`"
                             style="transition: transform 0.2s ease-out; will-change: transform;">
                            <!-- Logo with glow effect -->
                            <div class="relative">
                                <div class="absolute inset-0 blur-xl bg-primary/30 rounded-full scale-90"></div>
                                {{--
                                    Hinweis: Damit das Logo barrierefrei ist, sollte <x-application-logo /> ein <svg> mit <title>Minewache Logo</title> und ggf. role="img"/aria-labelledby enthalten.
                                --}}
                                <x-application-logo class="w-auto h-48 md:h-64 lg:h-80 fill-current text-primary drop-shadow-[0_0_25px_rgba(59,130,246,0.5)]" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Scroll indicator -->
            <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary/80" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                </svg>
            </div>
        </section>

        <!-- Feature cards with angled section divider -->
        <div class="relative mt-16">
            <!-- Angled section divider -->
            <div class="absolute top-0 left-0 right-0 h-24 transform -translate-y-full z-10 overflow-hidden">
                <div class="w-full h-full bg-base-100" style="clip-path: polygon(0 100%, 100% 100%, 100% 30%, 0 100%)"></div>
            </div>

            <!-- Features section -->
            <section class="bg-base-100 pt-16 pb-24 px-4 relative">
                <div class="absolute inset-0 opacity-20"
                     style="background-image: url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%239C92AC" fill-opacity="0.2"%3E%3Cpath d="M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');">
        </div>

        <div class="container mx-auto relative z-10">
            <div class="text-center mb-16">
                <h2 class="inline-block text-4xl md:text-5xl font-bold mb-4 text-transparent bg-clip-text bg-gradient-to-r from-primary to-secondary">
                    {{ __('messages.series_title') }}
                </h2>
                <div class="w-24 h-1 bg-primary mx-auto mt-2 mb-6 rounded-full"></div>
                <p class="text-xl text-base-content/80 max-w-3xl mx-auto">
                    Entdecke die spannende Welt der Minewache mit unseren einzigartigen Features
                </p>
            </div>

            <!-- Feature cards in hexagon-like grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Feature 1: Highlighted for application process -->
                <x-modern-card variant="gradient" size="lg" interactive="true" class="border-2 border-primary/30 bg-primary/10 hover:shadow-[0_10px_40px_rgba(59,130,246,0.3)]">
                    <div class="h-full flex flex-col">
                        <div class="p-3 bg-primary/20 rounded-xl w-16 h-16 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 animate-fade-in group-hover:animate-pulse" style="will-change: transform;">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold mb-4 group-hover:text-primary transition-colors duration-300">{{ __('messages.feature_1_title') }}</h3>
                        <p class="text-base-content/80 text-lg flex-grow">{{ __('messages.feature_1_description') }}</p>
                        <div class="mt-6">
                            <x-modern-button
                                variant="primary"
                                size="sm"
                                shape="pill"
                                href="{{ route('bewerben') }}"
                                icon='<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" /></svg>'
                                iconPosition="right"
                                class="animate-slide-up"
                            >
                                {{ __('messages.apply_now') }}
                            </x-modern-button>
                        </div>
                    </div>
                </x-modern-card>

                <!-- Feature 2 -->
                <x-modern-card variant="default" size="md">
                    <div class="p-8 h-full flex flex-col">
                        <div class="w-16 h-16 rounded-xl bg-primary/10 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 animate-fade-in group-hover:animate-pulse" style="will-change: transform;">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold mb-4 group-hover:text-primary transition-colors duration-300">{{ __('messages.feature_2_title') }}</h3>
                        <p class="text-base-content/80 text-lg flex-grow">{{ __('messages.feature_2_description') }}</p>
                        <div class="mt-6">
                            <x-modern-button variant="primary" href="{{ route('youtube') }}">
                                Episoden ansehen
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                </svg>
                            </x-modern-button>
                        </div>
                    </div>
                </x-modern-card>

                <!-- Feature 3 -->
                <x-modern-card variant="default" size="md">
                    <div class="p-8 h-full flex flex-col">
                        <div class="w-16 h-16 rounded-xl bg-primary/10 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 animate-fade-in group-hover:animate-pulse" style="will-change: transform;">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold mb-4 group-hover:text-primary transition-colors duration-300">{{ __('messages.feature_3_title') }}</h3>
                        <p class="text-base-content/80 text-lg flex-grow">{{ __('messages.feature_3_description') }}</p>
                        <div class="mt-6">
                            <x-modern-button variant="primary" href="{{ route('discord') }}">
                                Community beitreten
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                </svg>
                            </x-modern-button>
                        </div>
                    </div>
                </x-modern-card>
            </div>
        </section>
        </div>
    </main>

    <!-- Add particles.js for background effect -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Load particles.js if not already loaded
            if (typeof particlesJS === 'undefined') {
                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js';
                script.defer = true;
                script.onload = function() {
                    // Initialize particles after script is loaded
                    if (!window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
                        const isMobile = window.innerWidth < 768;
                        const particleCount = isMobile ? 30 : 60;
                        particlesJS('particles-js', {
                            particles: {
                                number: { value: particleCount, density: { enable: true, value_area: 800 } },
                                color: { value: '#3b82f6' },
                                opacity: { value: 0.2, random: true },
                                size: { value: 3, random: true },
                                line_linked: { enable: true, distance: 150, color: '#3b82f6', opacity: 0.1, width: 1 },
                                move: { enable: true, speed: 1, direction: 'none', random: true, out_mode: 'out' }
                            }
                        });
                    }
                };
                document.head.appendChild(script);
            } else {
                // Already loaded
                if (!window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
                    const isMobile = window.innerWidth < 768;
                    const particleCount = isMobile ? 30 : 60;
                    particlesJS('particles-js', {
                        particles: {
                            number: { value: particleCount, density: { enable: true, value_area: 800 } },
                            color: { value: '#3b82f6' },
                            opacity: { value: 0.2, random: true },
                            size: { value: 3, random: true },
                            line_linked: { enable: true, distance: 150, color: '#3b82f6', opacity: 0.1, width: 1 },
                            move: { enable: true, speed: 1, direction: 'none', random: true, out_mode: 'out' }
                        }
                    });
                }
            }
        });
    </script>

    @if(app()->environment('local'))
    <!-- Discord Login Debug Script (only in local environment) -->
    <script src="{{ asset('js/discord-login-debug.js') }}"></script>
    @endif
</x-app-layout>
