/**
 * Development Environment Manager
 *
 * This script manages the development environment for the Minewache website.
 * It handles:
 * - Killing any existing processes that might be using required ports
 * - Setting up unique port assignments for each service
 * - Starting services sequentially with proper error handling
 * - Ensuring clean shutdown
 *
 * This script is designed to work on both Windows and Linux/macOS environments.
 */
import { execSync, spawn } from 'child_process';
import { existsSync, readFileSync, writeFileSync } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name of the current module
const __dirname = path.dirname(fileURLToPath(import.meta.url));
const rootDir = path.resolve(__dirname, '..');

// ANSI color codes for terminal output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  bold: '\x1b[1m'
};

// Default port configuration
const DEFAULT_PORTS = {
  LARAVEL_SERVER: 8000,
  REVERB_SERVER: 8080,
  VITE_SERVER: 5173,
  DISCORD_BOT: 3001
};

// Process tracking
const processes = [];
let isShuttingDown = false;

/**
 * Log a message with timestamp and color
 */
function log(message, color = colors.white) {
  const timestamp = new Date().toISOString().replace(/T/, ' ').replace(/\..+/, '');
  console.log(`${color}[${timestamp}] ${message}${colors.reset}`);
}

/**
 * Kill processes that might be using our ports
 */
async function killExistingProcesses() {
  log('Stopping any existing processes...', colors.yellow);

  try {
    // Stop PHP Discord bot if running
    log('Checking if PHP Discord bot is running...', colors.cyan);
    try {
      // Verwende stop-discord.bat auf Windows, pkill auf Linux/Mac
      const isWindows = process.platform === 'win32';
      const stopCommand = isWindows ? 'stop-discord.bat' : "pkill -f 'php artisan minewache:run-discord-bot'";
      execSync(stopCommand, { stdio: 'pipe' });
      log('PHP Discord bot stopped', colors.green);
    } catch (error) {
      // Ignore errors, bot might not be running
    }

    // Stop any processes that might be using our ports
    const commands = [
      'pkill -f "php artisan serve"',
      'pkill -f "php artisan queue:work"',
      'pkill -f "php artisan queue:work-reverb"',
      'pkill -f "php artisan reverb:start"',
      'pkill -f "vite"'
    ];

    for (const cmd of commands) {
      try {
        execSync(cmd, { stdio: 'pipe' });
      } catch (error) {
        // Ignore errors, processes might not be running
      }
    }

    // Kill any processes using our ports
    const ports = [DEFAULT_PORTS.LARAVEL_SERVER, DEFAULT_PORTS.REVERB_SERVER, DEFAULT_PORTS.VITE_SERVER, DEFAULT_PORTS.DISCORD_BOT];

    for (const port of ports) {
      try {
        if (process.platform === 'win32') {
          // On Windows
          try {
            const netstatOutput = execSync(`netstat -ano | findstr :${port}`, { encoding: 'utf8' });
            const pidRegex = /\s+(\d+)$/gm;
            const matches = [...netstatOutput.matchAll(pidRegex)];
            const pids = [...new Set(matches.map(match => match[1]))];

            for (const pid of pids) {
              if (pid !== '0') {
                execSync(`taskkill /F /PID ${pid}`, { stdio: 'pipe' });
                log(`Killed process with PID ${pid} using port ${port}`, colors.yellow);
              }
            }
          } catch (error) {
            // Ignore errors, process might not be running
          }
        } else {
          // On Unix systems
          try {
            // Try lsof first
            const lsofOutput = execSync(`lsof -ti:${port}`, { encoding: 'utf8' });
            const pids = lsofOutput.trim().split('\n');

            for (const pid of pids) {
              if (pid) {
                execSync(`kill -9 ${pid}`, { stdio: 'pipe' });
                log(`Killed process with PID ${pid} using port ${port}`, colors.yellow);
              }
            }
          } catch (error) {
            // Try fuser as an alternative
            try {
              execSync(`fuser -k ${port}/tcp`, { stdio: 'pipe' });
              log(`Killed processes using port ${port}`, colors.yellow);
            } catch (fuserError) {
              // Ignore errors, process might not be running
            }
          }
        }
      } catch (error) {
        // Ignore errors, port might not be in use
      }
    }

    // Wait a moment to ensure ports are released
    await new Promise(resolve => setTimeout(resolve, 1000));
    log('Environment cleared', colors.green);
  } catch (error) {
    log(`Error clearing environment: ${error.message}`, colors.red);
  }
}

/**
 * Start a process and track it
 */
function startProcess(command, args, name, color) {
  log(`Starting ${name}...`, color);

  // Handle Windows-specific command adjustments
  let finalCommand = command;
  let finalArgs = args;

  if (process.platform === 'win32') {
    // On Windows, some commands need special handling
    if (command === 'php' && args[0] === 'artisan') {
      // Windows might need php.exe explicitly
      finalCommand = 'php.exe';
    } else if (command === 'npx') {
      // Windows might need npx.cmd explicitly
      finalCommand = 'npx.cmd';
    }
  }

  const proc = spawn(finalCommand, finalArgs, {
    stdio: 'pipe',
    shell: true,
    cwd: rootDir,
    // Set environment variables to ensure proper encoding on Windows
    env: { ...process.env, FORCE_COLOR: '1' }
  });

  processes.push({ proc, name, color });

  proc.stdout.on('data', (data) => {
    const lines = data.toString().trim().split('\n');
    for (const line of lines) {
      if (line.trim()) {
        log(`[${name}] ${line}`, color);
      }
    }
  });

  proc.stderr.on('data', (data) => {
    const lines = data.toString().trim().split('\n');
    for (const line of lines) {
      if (line.trim()) {
        log(`[${name}] ERROR: ${line}`, colors.red);
      }
    }
  });

  proc.on('error', (error) => {
    log(`[${name}] Failed to start process: ${error.message}`, colors.red);

    // Try alternative command on Windows if the first attempt fails
    if (process.platform === 'win32' && !error.message.includes('retry')) {
      log(`[${name}] Attempting alternative command...`, colors.yellow);

      // Create a retry flag to prevent infinite recursion
      const retryArgs = [...args];
      if (command === 'php') {
        // Try without .exe extension
        startProcess('php', retryArgs, `${name} (retry)`, color);
      } else if (command === 'npx') {
        // Try without .cmd extension
        startProcess('npx', retryArgs, `${name} (retry)`, color);
      }
    }
  });

  proc.on('close', (code) => {
    if (!isShuttingDown) {
      log(`[${name}] Process exited with code ${code}`, code === 0 ? color : colors.red);

      // Remove from tracked processes
      const index = processes.findIndex(p => p.proc === proc);
      if (index !== -1) {
        processes.splice(index, 1);
      }

      // If all processes have exited, exit the script
      if (processes.length === 0 && !isShuttingDown) {
        log('All processes have exited. Shutting down...', colors.yellow);
        process.exit(0);
      }
    }
  });

  return proc;
}

/**
 * Synchronize environment variables for Discord bot
 * Note: This function is no longer needed with the PHP Discord bot implementation
 */
async function syncDiscordEnv() {
  log('PHP Discord bot uses Laravel environment directly - no sync needed', colors.green);
}

/**
 * Start the development environment
 */
async function startDevEnvironment(options = {}) {
  const { includeDiscord = true, includeReverb = true } = options;

  log('Starting development environment...', colors.bold + colors.cyan);

  // Kill any existing processes
  await killExistingProcesses();

  // Sync Discord bot environment if needed
  if (includeDiscord) {
    await syncDiscordEnv();
  }

  // Start Laravel server
  startProcess('php', ['artisan', 'serve'], 'Laravel', colors.green);

  // Start Queue worker with Reverb broadcasting
  startProcess('php', ['artisan', 'queue:work-reverb', '--queue=default,media', '--timeout=0'], 'Queue', colors.yellow);

  // Start Vite
  startProcess('npx', ['vite'], 'Vite', colors.blue);

  // Start Reverb if needed
  if (includeReverb) {
    startProcess('php', ['artisan', 'reverb:start', '--debug'], 'Reverb', colors.magenta);
  }

  // Start Discord bot if needed
  if (includeDiscord) {
    // Set environment variable to show logs in development
    process.env.DISCORD_BOT_SHOW_LOGS = 'true';

    // Start the PHP Discord bot with logs visible
    if (process.platform === 'win32') {
      // On Windows, use start-discord.bat
      startProcess('start-discord.bat', [], 'Discord', colors.cyan);
    } else {
      // On Linux/Mac, use Artisan command directly
      startProcess('php', ['artisan', 'minewache:run-discord-bot'], 'Discord', colors.cyan);
    }
  }

  log('Development environment started successfully!', colors.bold + colors.green);
  log('Press Ctrl+C to stop all processes and exit.', colors.bold + colors.white);
}

/**
 * Shutdown all processes
 */
async function shutdown() {
  if (isShuttingDown) return;
  isShuttingDown = true;

  log('Shutting down development environment...', colors.yellow);

  // Stop all tracked processes
  for (const { proc, name, color } of processes) {
    log(`Stopping ${name}...`, color);
    proc.kill();
  }

  // Stop Discord bot explicitly
  try {
    log('Stopping Discord bot...', colors.cyan);
    if (process.platform === 'win32') {
      // On Windows, use stop-discord.bat
      execSync('stop-discord.bat', { stdio: 'pipe' });
    } else {
      // On Linux/Mac, kill the process
      execSync("pkill -f 'php artisan minewache:run-discord-bot'", { stdio: 'pipe' });
    }
    log('Discord bot stopped', colors.green);
  } catch (error) {
    // Ignore errors, bot might not be running
  }

  // Stop Reverb server explicitly
  try {
    log('Ensuring Reverb server is stopped...', colors.magenta);

    // Find and kill any processes listening on the Reverb port (6001)
    if (process.platform === 'win32') {
      try {
        // On Windows, use netstat and taskkill
        const netstatOutput = execSync('netstat -ano | findstr :6001', { encoding: 'utf8' });
        const pidRegex = /\s+(\d+)$/gm;
        const matches = [...netstatOutput.matchAll(pidRegex)];
        const pids = [...new Set(matches.map(match => match[1]))];

        for (const pid of pids) {
          if (pid !== '0') {
            execSync(`taskkill /F /PID ${pid}`, { stdio: 'pipe' });
            log(`Killed Reverb process with PID ${pid}`, colors.magenta);
          }
        }
      } catch (error) {
        // Ignore errors, process might not be running
      }
    } else {
      // On Unix systems, use lsof and kill
      try {
        // Find PIDs using port 6001
        const lsofOutput = execSync('lsof -ti:6001', { encoding: 'utf8' });
        const pids = lsofOutput.trim().split('\n');

        for (const pid of pids) {
          if (pid) {
            execSync(`kill -9 ${pid}`, { stdio: 'pipe' });
            log(`Killed Reverb process with PID ${pid}`, colors.magenta);
          }
        }
      } catch (error) {
        // Try alternative approach with fuser
        try {
          execSync('fuser -k 6001/tcp', { stdio: 'pipe' });
          log('Killed processes using port 6001', colors.magenta);
        } catch (fuserError) {
          // Try with pkill as last resort
          try {
            execSync('pkill -f "php artisan reverb:start"', { stdio: 'pipe' });
            log('Killed Reverb processes using pkill', colors.magenta);
          } catch (pkillError) {
            // All methods failed, but we can continue
          }
        }
      }
    }

    log('Reverb server stopped', colors.green);
  } catch (error) {
    log(`Warning: Could not verify Reverb server shutdown: ${error.message}`, colors.yellow);
  }

  log('Development environment shutdown complete', colors.green);
  process.exit(0);
}

// Handle script termination
process.on('SIGINT', shutdown);
process.on('SIGTERM', shutdown);

// Parse command line arguments
const args = process.argv.slice(2);
const mode = args[0] || 'standard';

// Start the development environment based on the mode
switch (mode) {
  case 'no-discord':
    startDevEnvironment({ includeDiscord: false, includeReverb: true });
    break;
  case 'no-reverb':
    startDevEnvironment({ includeDiscord: true, includeReverb: false });
    break;
  case 'minimal':
    startDevEnvironment({ includeDiscord: false, includeReverb: false });
    break;
  case 'standard':
  default:
    startDevEnvironment({ includeDiscord: true, includeReverb: true });
    break;
}
