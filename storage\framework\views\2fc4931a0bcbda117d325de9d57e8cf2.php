<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['position' => 'bottom-right', 'type' => 'simple']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['position' => 'bottom-right', 'type' => 'simple']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    $positionClasses = [
        'top-right' => 'top-4 right-4',
        'top-left' => 'top-4 left-4',
        'bottom-right' => 'bottom-4 right-4',
        'bottom-left' => 'bottom-4 left-4',
    ][$position] ?? 'bottom-4 right-4';
?>

<div
    x-data="enhancedThemeManager()"
    <?php echo e($attributes->merge(['class' => "fixed {$positionClasses} z-50"])); ?>

    role="region"
    aria-label="Theme switcher"
>
    <?php if($type === 'simple'): ?>
        <!-- Simple toggle button -->
        <button
            @click="toggleTheme()"
            class="btn btn-circle btn-md bg-primary text-primary-content hover:bg-primary-focus border-primary shadow-lg transition-all duration-300 hover:scale-110"
            aria-label="Toggle theme"
            :aria-pressed="currentTheme === 'minewache-light'"
        >
            <span class="sr-only" x-text="getThemeLabel()"></span>
            <!-- Sun icon for dark mode -->
            <svg
                x-show="currentTheme === 'minewache'"
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 transition-transform duration-300"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                x-transition:enter="transition ease-out duration-300"
                x-transition:enter-start="opacity-0 rotate-90"
                x-transition:enter-end="opacity-100 rotate-0"
                x-transition:leave="transition ease-in duration-300"
                x-transition:leave-start="opacity-100 rotate-0"
                x-transition:leave-end="opacity-0 -rotate-90"
            >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
            </svg>
            <!-- Moon icon for light mode -->
            <svg
                x-show="currentTheme === 'minewache-light'"
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 transition-transform duration-300"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                x-transition:enter="transition ease-out duration-300"
                x-transition:enter-start="opacity-0 rotate-90"
                x-transition:enter-end="opacity-100 rotate-0"
                x-transition:leave="transition ease-in duration-300"
                x-transition:leave-start="opacity-100 rotate-0"
                x-transition:leave-end="opacity-0 -rotate-90"
            >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
            </svg>
            <!-- Auto icon for auto mode -->
            <svg
                x-show="currentTheme === 'minewache-auto'"
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 transition-transform duration-300"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                x-transition:enter="transition ease-out duration-300"
                x-transition:enter-start="opacity-0 scale-50"
                x-transition:enter-end="opacity-100 scale-100"
                x-transition:leave="transition ease-in duration-300"
                x-transition:leave-start="opacity-100 scale-100"
                x-transition:leave-end="opacity-0 scale-50"
            >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            <!-- High contrast icon -->
            <svg
                x-show="currentTheme === 'minewache-high-contrast'"
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 transition-transform duration-300"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                x-transition:enter="transition ease-out duration-300"
                x-transition:enter-start="opacity-0 scale-50"
                x-transition:enter-end="opacity-100 scale-100"
                x-transition:leave="transition ease-in duration-300"
                x-transition:leave-start="opacity-100 scale-100"
                x-transition:leave-end="opacity-0 scale-50"
            >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
            <!-- Colorful icon -->
            <svg
                x-show="currentTheme === 'minewache-colorful'"
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 transition-transform duration-300"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                x-transition:enter="transition ease-out duration-300"
                x-transition:enter-start="opacity-0 scale-50"
                x-transition:enter-end="opacity-100 scale-100"
                x-transition:leave="transition ease-in duration-300"
                x-transition:leave-start="opacity-100 scale-100"
                x-transition:leave-end="opacity-0 scale-50"
            >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
            </svg>
        </button>
    <?php else: ?>
        <!-- Advanced dropdown selector -->
        <div class="relative" x-data="{ open: false }">
            <button
                @click="open = !open"
                class="btn btn-circle btn-md bg-primary text-primary-content hover:bg-primary-focus border-primary shadow-lg transition-all duration-300 hover:scale-110"
                aria-label="Open theme selector"
                :aria-expanded="open"
            >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
                </svg>
            </button>
            
            <!-- Theme selector dropdown -->
            <div
                x-show="open"
                @click.away="open = false"
                x-transition:enter="transition ease-out duration-200"
                x-transition:enter-start="opacity-0 scale-95 translate-y-2"
                x-transition:enter-end="opacity-100 scale-100 translate-y-0"
                x-transition:leave="transition ease-in duration-150"
                x-transition:leave-start="opacity-100 scale-100 translate-y-0"
                x-transition:leave-end="opacity-0 scale-95 translate-y-2"
                class="absolute bottom-full mb-2 right-0 w-64 glass rounded-2xl shadow-2xl border border-base-300/20 p-4"
            >
                <h3 class="text-sm font-semibold text-base-content mb-3">Choose Theme</h3>
                <div class="space-y-2">
                    <template x-for="theme in availableThemes" :key="theme.id">
                        <button
                            @click="setTheme(theme.id); open = false"
                            class="w-full flex items-center gap-3 p-3 rounded-xl transition-all duration-200 hover:bg-base-200/50"
                            :class="{ 'bg-primary/20 border border-primary/30': currentTheme === theme.id }"
                        >
                            <div class="w-6 h-6 rounded-full border-2 border-base-300 flex items-center justify-center" :style="`background: ${theme.preview}`">
                                <div x-show="currentTheme === theme.id" class="w-2 h-2 bg-primary rounded-full"></div>
                            </div>
                            <div class="flex-1 text-left">
                                <div class="text-sm font-medium text-base-content" x-text="theme.name"></div>
                                <div class="text-xs text-base-content/60" x-text="theme.description"></div>
                            </div>
                        </button>
                    </template>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Enhanced CSS variables for all themes -->
<style>
    :root {
        --color-primary-rgb: 59, 130, 246;
        --theme-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    [data-theme="minewache"] {
        --color-primary-rgb: 59, 130, 246;
    }

    [data-theme="minewache-light"] {
        --color-primary-rgb: 59, 130, 246;
    }

    [data-theme="minewache-auto"] {
        --color-primary-rgb: 59, 130, 246;
    }

    [data-theme="minewache-high-contrast"] {
        --color-primary-rgb: 0, 102, 255;
    }

    [data-theme="minewache-colorful"] {
        --color-primary-rgb: 139, 92, 246;
    }

    /* Smooth theme transitions */
    html {
        transition: var(--theme-transition);
    }

    body {
        transition: var(--theme-transition);
    }

    .glass {
        transition: var(--theme-transition);
    }
</style>

<?php $__env->startPush('scripts'); ?>
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('enhancedThemeManager', () => ({
            currentTheme: 'minewache',
            availableThemes: [
                {
                    id: 'minewache',
                    name: 'Dark',
                    description: 'Classic dark theme',
                    preview: 'linear-gradient(135deg, #0f172a 0%, #1e293b 100%)'
                },
                {
                    id: 'minewache-light',
                    name: 'Light',
                    description: 'Clean light theme',
                    preview: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)'
                },
                {
                    id: 'minewache-auto',
                    name: 'Auto',
                    description: 'Follows system preference',
                    preview: 'linear-gradient(135deg, #0f172a 0%, #f8fafc 100%)'
                },
                {
                    id: 'minewache-high-contrast',
                    name: 'High Contrast',
                    description: 'Enhanced accessibility',
                    preview: 'linear-gradient(135deg, #000000 0%, #ffffff 100%)'
                },
                {
                    id: 'minewache-colorful',
                    name: 'Colorful',
                    description: 'Vibrant and expressive',
                    preview: 'linear-gradient(135deg, #8b5cf6 0%, #06b6d4 50%, #f59e0b 100%)'
                }
            ],

            init() {
                this.loadTheme();
                this.setupSystemPreferenceListener();
            },

            loadTheme() {
                const savedTheme = localStorage.getItem('theme');
                if (savedTheme && this.availableThemes.find(t => t.id === savedTheme)) {
                    this.currentTheme = savedTheme;
                    this.applyTheme(savedTheme);
                } else {
                    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
                    this.currentTheme = prefersDark ? 'minewache' : 'minewache-light';
                    this.applyTheme(this.currentTheme);
                }
            },

            setupSystemPreferenceListener() {
                window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', e => {
                    if (this.currentTheme === 'minewache-auto' || !localStorage.getItem('theme')) {
                        const newTheme = e.matches ? 'minewache' : 'minewache-light';
                        this.currentTheme = newTheme;
                        this.applyTheme(newTheme);
                    }
                });
            },

            toggleTheme() {
                const themeOrder = ['minewache', 'minewache-light', 'minewache-auto'];
                const currentIndex = themeOrder.indexOf(this.currentTheme);
                const nextIndex = (currentIndex + 1) % themeOrder.length;
                this.setTheme(themeOrder[nextIndex]);
            },

            setTheme(themeId) {
                this.currentTheme = themeId;
                this.applyTheme(themeId);
                localStorage.setItem('theme', themeId);
                this.announceThemeChange(themeId);
            },

            applyTheme(theme) {
                // Handle auto theme
                if (theme === 'minewache-auto') {
                    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
                    theme = prefersDark ? 'minewache' : 'minewache-light';
                }

                document.documentElement.setAttribute('data-theme', theme);
                
                // Update body classes
                document.body.className = document.body.className.replace(/\b(light|dark)-mode\b/g, '');
                document.body.classList.add(theme.includes('light') ? 'light-mode' : 'dark-mode');

                // Dispatch theme change event
                window.dispatchEvent(new CustomEvent('theme-changed', {
                    detail: { 
                        theme: this.currentTheme, 
                        actualTheme: theme,
                        isDarkMode: !theme.includes('light') 
                    }
                }));
            },

            getThemeLabel() {
                const theme = this.availableThemes.find(t => t.id === this.currentTheme);
                return theme ? `Switch from ${theme.name} theme` : 'Switch theme';
            },

            announceThemeChange(themeId) {
                const theme = this.availableThemes.find(t => t.id === themeId);
                if (!theme) return;

                const announcement = document.createElement('div');
                announcement.setAttribute('aria-live', 'polite');
                announcement.classList.add('sr-only');
                announcement.textContent = `${theme.name} theme activated`;
                document.body.appendChild(announcement);

                setTimeout(() => {
                    if (document.body.contains(announcement)) {
                        document.body.removeChild(announcement);
                    }
                }, 3000);
            }
        }));
    });
</script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\minewache-website\resources\views/components/enhanced-theme-switcher.blade.php ENDPATH**/ ?>