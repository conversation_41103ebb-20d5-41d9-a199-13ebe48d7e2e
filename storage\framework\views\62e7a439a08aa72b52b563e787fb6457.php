<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "Die Minewache",
    "url": "<?php echo e(url('/')); ?>",
    "description": "Die offizielle Webseite der Minewache - Die beliebte Minecraft-Polizeiserie mit spannenden Verfolgungsjagden, Kriminalfällen und Rollenspiel im Minecraft-Universum",
    "potentialAction": {
        "@type": "SearchAction",
        "target": "<?php echo e(url('/')); ?>?search={search_term_string}",
        "query-input": "required name=search_term_string"
    }
}
</script>

<?php if(isset($youtubeLinks) && $youtubeLinks->isNotEmpty()): ?>
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "TVSeries",
    "name": "Die Minewache",
    "description": "Eine strukturierte Minecraft-Webserie, die ein Polizei-Rollenspiel im Minecraft-Universum darstellt. Die Serie kombiniert narrative Elemente mit Action-Sequenzen.",
    "genre": ["Minecraft", "Polizei", "Rollenspiel", "Action", "Krimi"],
    "countryOfOrigin": {
        "@type": "Country",
        "name": "Deutschland"
    },
    "inLanguage": "de-DE",
    "numberOfSeasons": <?php echo e($youtubeLinks->max('season')); ?>,
    "actor": [
        {
            "@type": "Person",
            "name": "Richard"
        },
        {
            "@type": "Person",
            "name": "Herr Horn"
        }
    ],
    "director": {
        "@type": "Person",
        "name": "Sarocesch"
    },
    "musicBy": {
        "@type": "Person",
        "name": "Ferdmusic"
    },
    "url": "<?php echo e(url('/youtube')); ?>",
    "sameAs": [
        "https://www.youtube.com/channel/UCxxxxxxxx",
        "https://discord.gg/wwrK2csZnX"
    ]
}
</script>
<?php endif; ?>

<?php if(isset($featuredVideo) && $featuredVideo): ?>
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "VideoObject",
    "name": "<?php echo e($featuredVideo->title ?: 'Die Minewache - Staffel ' . $featuredVideo->season . ' Folge ' . $featuredVideo->episode); ?>",
    "description": "<?php echo e($featuredVideo->description ?: 'Die Minewache - Eine spannende Minecraft-Polizeiserie mit Action und Rollenspiel'); ?>",
    "thumbnailUrl": "<?php echo e($featuredVideo->thumbnail_url ?: asset('og-image.png')); ?>",
    "uploadDate": "<?php echo e($featuredVideo->published_at ? $featuredVideo->published_at->toIso8601String() : now()->toIso8601String()); ?>",
    "duration": "<?php echo e($featuredVideo->duration_seconds ? 'PT'.$featuredVideo->duration_seconds.'S' : 'PT0S'); ?>",
    "contentUrl": "https://www.youtube.com/watch?v=<?php echo e($featuredVideo->link); ?>",
    "embedUrl": "https://www.youtube.com/embed/<?php echo e($featuredVideo->link); ?>"
}
</script>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\minewache-website\resources\views/partials/structured-data.blade.php ENDPATH**/ ?>