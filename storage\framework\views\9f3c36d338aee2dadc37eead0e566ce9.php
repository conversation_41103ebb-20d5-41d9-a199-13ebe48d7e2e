<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['active']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['active']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
$classes = ($active ?? false)
            ? 'relative inline-flex items-center px-3 py-4 text-sm font-medium text-info border-b-4 border-primary transition-all duration-300 ease-in-out'
            : 'relative inline-flex items-center px-3 py-4 text-sm font-medium text-base-content hover:text-primary border-b-4 border-transparent hover:border-primary/40 transition-all duration-300 ease-in-out';
?>

<a <?php echo e($attributes->merge(['class' => $classes])); ?>>
    <span class="relative z-10">
        <?php echo e($slot); ?>

        <?php if($active ?? false): ?>
            <span class="absolute inset-x-0 -bottom-1  bg-primary transform scale-x-100 origin-left transition-transform duration-300"></span>
        <?php else: ?>
            <span class="absolute inset-x-0 -bottom-1 h-0.5 bg-primary transform scale-x-0 origin-left group-hover:scale-x-100 transition-transform duration-300"></span>
        <?php endif; ?>
    </span>
</a>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\minewache-website\resources\views/components/nav-link.blade.php ENDPATH**/ ?>