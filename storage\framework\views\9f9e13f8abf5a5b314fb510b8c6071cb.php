<div class="application-wizard min-h-screen bg-gradient-to-br from-base-100 via-base-100 to-base-200/30">
    <!-- Application Wizard - Modern Open Design -->
    <!-- Hero Section with Progress -->
    <div class="relative overflow-hidden">
        <!-- Background decorative elements -->
        <div class="absolute inset-0 overflow-hidden pointer-events-none">
            <div class="absolute -top-40 -right-40 w-80 h-80 bg-primary/5 rounded-full blur-3xl"></div>
            <div class="absolute top-1/2 -left-40 w-60 h-60 bg-secondary/5 rounded-full blur-2xl"></div>
        </div>

        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-12 pb-16">
            <!-- Header -->
            <div class="text-center mb-12">
                <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-primary to-secondary mb-4">
                    <?php echo e(__('application.application_form')); ?>

                </h1>
                <p class="text-xl text-base-content/70 max-w-2xl mx-auto">
                    <?php echo e(__('application.application_description')); ?>

                </p>
            </div>

            <!-- Modern Progress Indicator -->
            <div class="max-w-4xl mx-auto mb-16">
                <div class="relative">
                    <!-- Progress Line -->
                    <div class="absolute top-6 left-0 w-full h-0.5 bg-base-300"></div>
                    <div class="absolute top-6 left-0 h-0.5 bg-gradient-to-r from-primary to-secondary transition-all duration-700 ease-out"
                         style="width: <?php echo e((($currentStep - 1) / 3) * 100); ?>%"></div>

                    <!-- Step Indicators -->
                    <div class="relative flex justify-between">
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = [
                            ['step' => 1, 'title' => __('application.personal_data'), 'icon' => 'user'],
                            ['step' => 2, 'title' => __('application.role_specific'), 'icon' => 'briefcase'],
                            ['step' => 3, 'title' => __('application.about_you'), 'icon' => 'heart'],
                            ['step' => 4, 'title' => __('application.review'), 'icon' => 'check-circle']
                        ]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $stepData): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="flex flex-col items-center group">
                                <div class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                                    'w-12 h-12 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-300 mb-3',
                                    'bg-gradient-to-r from-primary to-secondary text-white shadow-lg scale-110' => $currentStep >= $stepData['step'],
                                    'bg-base-200 text-base-content/60 group-hover:bg-base-300' => $currentStep < $stepData['step']
                                ]); ?>">
                                    <!--[if BLOCK]><![endif]--><?php if($currentStep > $stepData['step']): ?>
                                        <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-check'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'h-6 w-6']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                    <?php else: ?>
                                        <!--[if BLOCK]><![endif]--><?php switch($stepData['icon']):
                                            case ('user'): ?>
                                                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-user'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'h-6 w-6']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                                <?php break; ?>
                                            <?php case ('briefcase'): ?>
                                                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-briefcase'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'h-6 w-6']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                                <?php break; ?>
                                            <?php case ('heart'): ?>
                                                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-heart'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'h-6 w-6']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                                <?php break; ?>
                                            <?php case ('check-circle'): ?>
                                                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-check-circle'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'h-6 w-6']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                                <?php break; ?>
                                        <?php endswitch; ?><!--[if ENDBLOCK]><![endif]-->
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                                <span class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                                    'text-sm font-medium text-center transition-colors duration-300',
                                    'text-primary' => $currentStep == $stepData['step'],
                                    'text-base-content' => $currentStep > $stepData['step'],
                                    'text-base-content/60' => $currentStep < $stepData['step']
                                ]); ?>">
                                    <?php echo e($stepData['title']); ?>

                                </span>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
        <div class="animate-fade-in">
            <!-- Step 1: Personal Information -->
            <!--[if BLOCK]><![endif]--><?php if($currentStep == 1): ?>
                <div class="space-y-12 animate-slide-up">
                    <!-- Section Header -->
                    <div class="text-center">
                        <h2 class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-primary to-secondary mb-4">
                            <?php echo e(__('application.personal_data')); ?>

                        </h2>
                        <p class="text-lg text-base-content/70">
                            <?php echo e(__('application.personal_data_description')); ?>

                        </p>
                    </div>

                    <!-- GDPR Data Notice -->
                    <div class="bg-info/10 border border-info/20 rounded-2xl p-6">
                        <?php if (isset($component)) { $__componentOriginald24eb1b892d70d64bf2e573a32cfdc43 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald24eb1b892d70d64bf2e573a32cfdc43 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.gdpr-data-notice','data' => ['type' => 'application']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('gdpr-data-notice'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'application']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald24eb1b892d70d64bf2e573a32cfdc43)): ?>
<?php $attributes = $__attributesOriginald24eb1b892d70d64bf2e573a32cfdc43; ?>
<?php unset($__attributesOriginald24eb1b892d70d64bf2e573a32cfdc43); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald24eb1b892d70d64bf2e573a32cfdc43)): ?>
<?php $component = $__componentOriginald24eb1b892d70d64bf2e573a32cfdc43; ?>
<?php unset($__componentOriginald24eb1b892d70d64bf2e573a32cfdc43); ?>
<?php endif; ?>
                    </div>

                    <!-- Personal Information Form -->
                    <div class="space-y-8">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                            <!-- Name -->
                            <div class="space-y-2">
                                <label for="name" class="block text-sm font-semibold text-base-content/90">
                                    <?php echo e(__('application.name')); ?>

                                </label>
                                <?php if (isset($component)) { $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-input','data' => ['type' => 'text','placeholder' => ''.e(__('application.name_placeholder')).'','variant' => 'outlined','id' => 'name','wire:model.live' => 'name','class' => 'w-full text-lg py-4']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-input'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'text','placeholder' => ''.e(__('application.name_placeholder')).'','variant' => 'outlined','id' => 'name','wire:model.live' => 'name','class' => 'w-full text-lg py-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $attributes = $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $component = $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
                                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="text-error text-sm flex items-center gap-2">
                                        <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-exclamation-circle'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-4 h-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                        <?php echo e($message); ?>

                                    </span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                <?php if (isset($component)) { $__componentOriginald1372deb1861675a162fefcdfeada83c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald1372deb1861675a162fefcdfeada83c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.gdpr-field-explanation','data' => ['field' => 'name']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('gdpr-field-explanation'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['field' => 'name']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald1372deb1861675a162fefcdfeada83c)): ?>
<?php $attributes = $__attributesOriginald1372deb1861675a162fefcdfeada83c; ?>
<?php unset($__attributesOriginald1372deb1861675a162fefcdfeada83c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald1372deb1861675a162fefcdfeada83c)): ?>
<?php $component = $__componentOriginald1372deb1861675a162fefcdfeada83c; ?>
<?php unset($__componentOriginald1372deb1861675a162fefcdfeada83c); ?>
<?php endif; ?>
                            </div>

                            <!-- Age -->
                            <div class="space-y-2">
                                <label for="age" class="block text-sm font-semibold text-base-content/90">
                                    <?php echo e(__('application.age')); ?>

                                </label>
                                <?php if (isset($component)) { $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-input','data' => ['type' => 'number','placeholder' => ''.e(__('application.age_placeholder')).'','variant' => 'outlined','id' => 'age','wire:model.live' => 'age','min' => '12','max' => '120','class' => 'w-full text-lg py-4']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-input'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'number','placeholder' => ''.e(__('application.age_placeholder')).'','variant' => 'outlined','id' => 'age','wire:model.live' => 'age','min' => '12','max' => '120','class' => 'w-full text-lg py-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $attributes = $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $component = $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
                                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['age'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="text-error text-sm flex items-center gap-2">
                                        <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-exclamation-circle'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-4 h-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                        <?php echo e($message); ?>

                                    </span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                <?php if (isset($component)) { $__componentOriginald1372deb1861675a162fefcdfeada83c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald1372deb1861675a162fefcdfeada83c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.gdpr-field-explanation','data' => ['field' => 'age']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('gdpr-field-explanation'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['field' => 'age']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald1372deb1861675a162fefcdfeada83c)): ?>
<?php $attributes = $__attributesOriginald1372deb1861675a162fefcdfeada83c; ?>
<?php unset($__attributesOriginald1372deb1861675a162fefcdfeada83c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald1372deb1861675a162fefcdfeada83c)): ?>
<?php $component = $__componentOriginald1372deb1861675a162fefcdfeada83c; ?>
<?php unset($__componentOriginald1372deb1861675a162fefcdfeada83c); ?>
<?php endif; ?>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                            <!-- Gender -->
                            <div class="space-y-2">
                                <label for="gender" class="block text-sm font-semibold text-base-content/90">
                                    <?php echo e(__('application.gender')); ?>

                                </label>
                                <select id="gender" wire:model.live="gender" class="select select-bordered w-full text-lg py-4 bg-base-100 border-base-300 focus:border-primary">
                                    <option value=""><?php echo e(__('application.gender_select')); ?></option>
                                    <option value="male"><?php echo e(__('application.gender_male')); ?></option>
                                    <option value="female"><?php echo e(__('application.gender_female')); ?></option>
                                    <option value="diverse"><?php echo e(__('application.gender_diverse')); ?></option>
                                    <option value="prefer_not_to_say"><?php echo e(__('application.gender_no_info')); ?></option>
                                </select>
                                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['gender'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="text-error text-sm flex items-center gap-2">
                                        <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-exclamation-circle'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-4 h-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                        <?php echo e($message); ?>

                                    </span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                            </div>

                            <!-- Pronouns -->
                            <div class="space-y-2">
                                <label for="pronouns" class="block text-sm font-semibold text-base-content/90">
                                    <?php echo e(__('application.pronouns')); ?>

                                    <span class="text-base-content/50 font-normal">(<?php echo e(__('application.optional')); ?>)</span>
                                </label>
                                <?php if (isset($component)) { $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-input','data' => ['type' => 'text','placeholder' => ''.e(__('application.pronouns_placeholder')).'','variant' => 'outlined','id' => 'pronouns','wire:model.live' => 'pronouns','class' => 'w-full text-lg py-4']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-input'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'text','placeholder' => ''.e(__('application.pronouns_placeholder')).'','variant' => 'outlined','id' => 'pronouns','wire:model.live' => 'pronouns','class' => 'w-full text-lg py-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $attributes = $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $component = $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Professions -->
                    <div>
                        <h3 class="text-lg font-semibold mb-4 text-center"><?php echo e(__('application.professions_question')); ?></h3>
                        <p class="text-base-content/70 mb-6 text-center"><?php echo e(__('application.professions_select_description')); ?></p>

                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                            <?php
                                $professionIcons = [
                                    'actor' => ['gradient' => 'from-purple-500 to-pink-500', 'icon' => 'user'],
                                    'actor_no_voice' => ['gradient' => 'from-blue-500 to-cyan-500', 'icon' => 'eye'],
                                    'voice_actor' => ['gradient' => 'from-green-500 to-emerald-500', 'icon' => 'microphone'],
                                    'builder' => ['gradient' => 'from-orange-500 to-red-500', 'icon' => 'cog'],
                                    'designer' => ['gradient' => 'from-indigo-500 to-purple-500', 'icon' => 'pencil'],
                                    'developer' => ['gradient' => 'from-yellow-500 to-orange-500', 'icon' => 'code'],
                                    'cutter' => ['gradient' => 'from-pink-500 to-rose-500', 'icon' => 'film'],
                                    'cameraman' => ['gradient' => 'from-teal-500 to-cyan-500', 'icon' => 'camera'],
                                    'modeler' => ['gradient' => 'from-violet-500 to-purple-500', 'icon' => 'cube'],
                                    'music_producer' => ['gradient' => 'from-emerald-500 to-teal-500', 'icon' => 'musical-note'],
                                    'other' => ['gradient' => 'from-gray-500 to-slate-500', 'icon' => 'ellipsis-horizontal']
                                ];
                            ?>

                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $professionOptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                    $config = $professionIcons[$value] ?? $professionIcons['other'];
                                    $isSelected = in_array($value, $professions ?? []);
                                ?>
                                <div class="profession-card p-4 border-2 border-base-300 rounded-xl cursor-pointer bg-base-200/30 hover:bg-base-200/50 transition-all duration-300 hover:transform hover:-translate-y-0.5 hover:shadow-lg <?php if($isSelected): ?> selected border-primary bg-gradient-to-br from-primary/20 to-secondary/20 <?php endif; ?>"
                                     wire:click="toggleProfession('<?php echo e($value); ?>')">
                                    <div class="flex items-center justify-between mb-3">
                                        <div class="flex items-center">
                                            <div class="w-10 h-10 bg-gradient-to-br <?php echo e($config['gradient']); ?> rounded-lg flex items-center justify-center mr-3">
                                                <!--[if BLOCK]><![endif]--><?php switch($config['icon']):
                                                    case ('user'): ?>
                                                        <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-user'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-5 h-5 text-white']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                                        <?php break; ?>
                                                    <?php case ('eye'): ?>
                                                        <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-eye'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-5 h-5 text-white']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                                        <?php break; ?>
                                                    <?php case ('microphone'): ?>
                                                        <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-microphone'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-5 h-5 text-white']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                                        <?php break; ?>
                                                    <?php case ('cog'): ?>
                                                        <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-cog-6-tooth'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-5 h-5 text-white']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                                        <?php break; ?>
                                                    <?php case ('pencil'): ?>
                                                        <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-pencil'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-5 h-5 text-white']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                                        <?php break; ?>
                                                    <?php case ('code'): ?>
                                                        <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-code-bracket'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-5 h-5 text-white']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                                        <?php break; ?>
                                                    <?php case ('film'): ?>
                                                        <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-film'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-5 h-5 text-white']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                                        <?php break; ?>
                                                    <?php case ('camera'): ?>
                                                        <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-camera'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-5 h-5 text-white']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                                        <?php break; ?>
                                                    <?php case ('cube'): ?>
                                                        <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-cube'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-5 h-5 text-white']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                                        <?php break; ?>
                                                    <?php case ('musical-note'): ?>
                                                        <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-musical-note'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-5 h-5 text-white']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                                        <?php break; ?>
                                                    <?php default: ?>
                                                        <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-ellipsis-horizontal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-5 h-5 text-white']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                                <?php endswitch; ?><!--[if ENDBLOCK]><![endif]-->
                                            </div>
                                            <div>
                                                <h4 class="font-semibold"><?php echo e($label); ?></h4>
                                                <p class="text-xs text-base-content/70"><?php echo e(__('application.profession_subtitle.' . $value)); ?></p>
                                            </div>
                                        </div>
                                        <div class="checkbox-indicator w-5 h-5 border-2 border-base-300 rounded flex items-center justify-center transition-all duration-300 <?php if($isSelected): ?> bg-primary border-primary <?php endif; ?>">
                                            <!--[if BLOCK]><![endif]--><?php if($isSelected): ?>
                                                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-check'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-3 h-3 text-white']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        </div>
                                    </div>
                                    <p class="text-sm text-base-content/80"><?php echo e(__('application.profession_description_long.' . $value)); ?></p>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['professions'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <span class="text-error text-sm block mt-4"><?php echo e($message); ?></span>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                        <!--[if BLOCK]><![endif]--><?php if(session()->has('professions_error')): ?>
                            <span class="text-error text-sm block mt-4"><?php echo e(session('professions_error')); ?></span>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    </div>

                    <!-- Other Profession -->
                    <!--[if BLOCK]><![endif]--><?php if(in_array('other', $professions ?? [])): ?>
                        <div class="animate-fade-in">
                            <label for="otherProfession" class="block text-sm font-medium"><?php echo e(__('application.other_profession')); ?></label>
                            <?php if (isset($component)) { $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-input','data' => ['type' => 'text','placeholder' => ''.e(__('application.other_profession_placeholder')).'','variant' => 'outlined','id' => 'otherProfession','wire:model.live' => 'otherProfession']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-input'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'text','placeholder' => ''.e(__('application.other_profession_placeholder')).'','variant' => 'outlined','id' => 'otherProfession','wire:model.live' => 'otherProfession']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $attributes = $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $component = $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['otherProfession'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-error text-sm"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

            <!-- Step 2: Role-specific Information -->
            <!--[if BLOCK]><![endif]--><?php if($currentStep == 2): ?>
                <div class="space-y-6 animate-slide-up">
                    <h2 class="text-xl font-semibold mb-4"><?php echo e(__('application.role_specific')); ?></h2>

                    <!-- Notice if no professions selected -->
                    <!--[if BLOCK]><![endif]--><?php if(empty($professions)): ?>
                        <div class="alert alert-warning">
                            <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-exclamation-triangle'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'h-6 w-6']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                            <span><?php echo e(__('application.please_select_at_least_one_profession')); ?></span>
                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                    <!-- Different fields based on selected professions -->
                    <!--[if BLOCK]><![endif]--><?php if(in_array('actor', $professions ?? []) || in_array('voice_actor', $professions ?? [])): ?>
                        <div class="card bg-base-300/50 p-4 mb-6 animate-fade-in">
                            <h3 class="font-medium mb-3"><?php echo e(__('application.acting_voice_acting')); ?>:</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="voice_type" class="block text-sm font-medium"><?php echo e(__('application.voice_type')); ?></label>
                                    <select id="voice_type" wire:model.live="voice_type" class="mt-1 select select-bordered w-full">
                                        <option value=""><?php echo e(__('application.gender_select')); ?></option>
                                        <option value="deep"><?php echo e(__('application.voice_type_deep')); ?></option>
                                        <option value="medium"><?php echo e(__('application.voice_type_medium')); ?></option>
                                        <option value="high"><?php echo e(__('application.voice_type_high')); ?></option>
                                    </select>
                                </div>
                                <div>
                                    <label for="microphone" class="block text-sm font-medium"><?php echo e(__('application.microphone')); ?></label>
                                    <?php if (isset($component)) { $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-input','data' => ['type' => 'text','placeholder' => ''.e(__('application.microphone_placeholder')).'','variant' => 'outlined','id' => 'microphone','wire:model.live' => 'microphone']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-input'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'text','placeholder' => ''.e(__('application.microphone_placeholder')).'','variant' => 'outlined','id' => 'microphone','wire:model.live' => 'microphone']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $attributes = $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $component = $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                    <?php
                        $pcRelevantProfessions = ['builder', 'cameraman', 'actor', 'actor_no_voice', 'designer', 'voice_actor', 'cutter', 'developer'];
                        $minecraftRelevantProfessions = ['builder', 'cameraman', 'actor', 'actor_no_voice'];
                        $gpuRelevantProfessions = ['builder', 'cameraman', 'actor', 'actor_no_voice', 'cutter'];
                    ?>

                    <!--[if BLOCK]><![endif]--><?php if(array_intersect($pcRelevantProfessions, $professions ?? [])): ?>
                        <div class="card bg-base-300/50 p-4 mb-6 animate-fade-in">
                            <h3 class="font-medium mb-3"><?php echo e(__('application.builder_cameraman')); ?>:</h3>

                            <!-- PC Checkbox -->
                            <div class="mb-3">
                                <div class="relative">
                                    <input type="checkbox" id="has_pc" wire:model="has_pc" class="peer sr-only">
                                    <label for="has_pc" class="flex items-center p-3 bg-white border-2 border-gray-200 rounded-lg cursor-pointer
               peer-checked:border-blue-600 peer-checked:text-blue-600 peer-checked:bg-blue-50 hover:text-gray-600 hover:bg-gray-50
               dark:hover:text-gray-300 dark:border-gray-700 dark:peer-checked:text-blue-500 dark:peer-checked:bg-blue-900/20
               dark:text-gray-400 dark:bg-gray-800 dark:hover:bg-gray-700">
                                        <span class="text-sm font-medium"><?php echo e(__('application.Hast du einen PC?')); ?></span>
                                    </label>
                                </div>
                            </div>

                            <!-- Minecraft-related fields -->
                            <!--[if BLOCK]><![endif]--><?php if(array_intersect($minecraftRelevantProfessions, $professions ?? [])): ?>
                                <!-- Modrinth Checkbox -->
                                <div class="mb-3">
                                    <div class="relative">
                                        <input type="checkbox" id="has_curseforge" wire:model="has_curseforge" class="peer sr-only">
                                        <label for="has_curseforge" class="flex items-center p-3 bg-white border-2 border-gray-200 rounded-lg cursor-pointer
                           peer-checked:border-blue-600 peer-checked:text-blue-600 hover:text-gray-600 hover:bg-gray-50
                           dark:hover:text-gray-300 dark:border-gray-700 dark:peer-checked:text-blue-500
                           dark:text-gray-400 dark:bg-gray-800 dark:hover:bg-gray-700">
                                            <span class="text-sm font-medium"><?php echo e(__('application.Hast du Modrinth?')); ?></span>
                                        </label>
                                    </div>
                                </div>

                                <!-- Minecraft Java Checkbox -->
                                <div class="mb-3">
                                    <div class="relative">
                                        <input type="checkbox" id="has_minecraft_java" wire:model="has_minecraft_java" class="peer sr-only">
                                        <label for="has_minecraft_java" class="flex items-center p-3 bg-white border-2 border-gray-200 rounded-lg cursor-pointer
                           peer-checked:border-blue-600 peer-checked:text-blue-600 hover:text-gray-600 hover:bg-gray-50
                           dark:hover:text-gray-300 dark:border-gray-700 dark:peer-checked:text-blue-500
                           dark:text-gray-400 dark:bg-gray-800 dark:hover:bg-gray-700">
                                            <span class="text-sm font-medium"><?php echo e(__('application.Hast du Minecraft Java?')); ?></span>
                                        </label>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label for="ram" class="block text-sm font-medium"><?php echo e(__('application.ram')); ?></label>
                                        <?php if (isset($component)) { $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-input','data' => ['type' => 'text','placeholder' => ''.e(__('application.ram_placeholder')).'','variant' => 'outlined','id' => 'ram','wire:model.live' => 'ram']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-input'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'text','placeholder' => ''.e(__('application.ram_placeholder')).'','variant' => 'outlined','id' => 'ram','wire:model.live' => 'ram']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $attributes = $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $component = $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
                                        <?php if (isset($component)) { $__componentOriginald1372deb1861675a162fefcdfeada83c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald1372deb1861675a162fefcdfeada83c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.gdpr-field-explanation','data' => ['field' => 'ram']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('gdpr-field-explanation'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['field' => 'ram']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald1372deb1861675a162fefcdfeada83c)): ?>
<?php $attributes = $__attributesOriginald1372deb1861675a162fefcdfeada83c; ?>
<?php unset($__attributesOriginald1372deb1861675a162fefcdfeada83c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald1372deb1861675a162fefcdfeada83c)): ?>
<?php $component = $__componentOriginald1372deb1861675a162fefcdfeada83c; ?>
<?php unset($__componentOriginald1372deb1861675a162fefcdfeada83c); ?>
<?php endif; ?>
                                    </div>
                                    <div>
                                        <label for="fps" class="block text-sm font-medium"><?php echo e(__('application.fps')); ?></label>
                                        <select id="fps" wire:model.live="fps" class="mt-1 select select-bordered w-full">
                                            <option value=""><?php echo e(__('application.gender_select')); ?></option>
                                            <option value="<10"><?php echo e(__('application.<10')); ?></option>
                                            <option value="10-"><?php echo e(__('application.10')); ?></option>
                                            <option value="20"><?php echo e(__('application.20')); ?></option>
                                            <option value="30"><?php echo e(__('application.30')); ?></option>
                                            <option value="40"><?php echo e(__('application.40')); ?></option>
                                            <option value="50"><?php echo e(__('application.50')); ?></option>
                                            <option value="60<"><?php echo e(__('application.>60')); ?></option>
                                        </select>
                                    </div>
                                </div>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                            <!-- GPU-related field -->
                            <!--[if BLOCK]><![endif]--><?php if(array_intersect($gpuRelevantProfessions, $professions ?? [])): ?>
                                <div>
                                    <label for="gpu" class="block text-sm font-medium"><?php echo e(__('application.gpu')); ?></label>
                                    <?php if (isset($component)) { $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-input','data' => ['type' => 'text','placeholder' => ''.e(__('application.gpu_placeholder')).'','variant' => 'outlined','id' => 'gpu','wire:model.live' => 'gpu']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-input'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'text','placeholder' => ''.e(__('application.gpu_placeholder')).'','variant' => 'outlined','id' => 'gpu','wire:model.live' => 'gpu']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $attributes = $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $component = $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
                                    <?php if (isset($component)) { $__componentOriginald1372deb1861675a162fefcdfeada83c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald1372deb1861675a162fefcdfeada83c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.gdpr-field-explanation','data' => ['field' => 'gpu']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('gdpr-field-explanation'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['field' => 'gpu']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald1372deb1861675a162fefcdfeada83c)): ?>
<?php $attributes = $__attributesOriginald1372deb1861675a162fefcdfeada83c; ?>
<?php unset($__attributesOriginald1372deb1861675a162fefcdfeada83c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald1372deb1861675a162fefcdfeada83c)): ?>
<?php $component = $__componentOriginald1372deb1861675a162fefcdfeada83c; ?>
<?php unset($__componentOriginald1372deb1861675a162fefcdfeada83c); ?>
<?php endif; ?>
                                </div>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                    <!--[if BLOCK]><![endif]--><?php if(in_array('designer', $professions ?? [])): ?>
                        <div class="card bg-base-300/50 p-4 mb-6 animate-fade-in">
                            <h3 class="font-medium mb-3"><?php echo e(__('application.designer')); ?>:</h3>
                            <div class="grid grid-cols-1 gap-4">
                                <div>
                                    <label for="program" class="block text-sm font-medium"><?php echo e(__('application.design_programs')); ?></label>
                                    <?php if (isset($component)) { $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-input','data' => ['type' => 'text','placeholder' => ''.e(__('application.design_programs_placeholder')).'','variant' => 'outlined','id' => 'program','wire:model.live' => 'program']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-input'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'text','placeholder' => ''.e(__('application.design_programs_placeholder')).'','variant' => 'outlined','id' => 'program','wire:model.live' => 'program']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $attributes = $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $component = $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
                                </div>
                                <div>
                                    <label for="design_style" class="block text-sm font-medium"><?php echo e(__('application.design_style')); ?></label>
                                    <?php if (isset($component)) { $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-input','data' => ['type' => 'text','placeholder' => ''.e(__('application.design_style_placeholder')).'','variant' => 'outlined','id' => 'design_style','wire:model.live' => 'design_style']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-input'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'text','placeholder' => ''.e(__('application.design_style_placeholder')).'','variant' => 'outlined','id' => 'design_style','wire:model.live' => 'design_style']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $attributes = $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $component = $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
                                </div>
                                <div>
                                    <label for="favorite_design" class="block text-sm font-medium"><?php echo e(__('application.favorite_design')); ?></label>
                                    <?php if (isset($component)) { $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-input','data' => ['type' => 'text','placeholder' => ''.e(__('application.favorite_design_placeholder')).'','variant' => 'outlined','id' => 'favorite_design','wire:model.live' => 'favorite_design']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-input'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'text','placeholder' => ''.e(__('application.favorite_design_placeholder')).'','variant' => 'outlined','id' => 'favorite_design','wire:model.live' => 'favorite_design']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $attributes = $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $component = $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                    <!--[if BLOCK]><![endif]--><?php if(in_array('developer', $professions ?? [])): ?>
                        <div class="card bg-base-300/50 p-4 mb-6 animate-fade-in">
                            <h3 class="font-medium mb-3"><?php echo e(__('application.developer')); ?>:</h3>
                            <div class="grid grid-cols-1 gap-4">
                                <div>
                                    <label for="languages" class="block text-sm font-medium"><?php echo e(__('application.programming_languages')); ?></label>
                                    <?php if (isset($component)) { $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-input','data' => ['type' => 'text','placeholder' => ''.e(__('application.programming_languages_placeholder')).'','variant' => 'outlined','id' => 'languages','wire:model.live' => 'languages']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-input'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'text','placeholder' => ''.e(__('application.programming_languages_placeholder')).'','variant' => 'outlined','id' => 'languages','wire:model.live' => 'languages']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $attributes = $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $component = $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
                                </div>
                                <div>
                                    <label for="ide" class="block text-sm font-medium"><?php echo e(__('application.preferred_ide')); ?></label>
                                    <?php if (isset($component)) { $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-input','data' => ['type' => 'text','placeholder' => ''.e(__('application.preferred_ide_placeholder')).'','variant' => 'outlined','id' => 'ide','wire:model.live' => 'ide']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-input'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'text','placeholder' => ''.e(__('application.preferred_ide_placeholder')).'','variant' => 'outlined','id' => 'ide','wire:model.live' => 'ide']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $attributes = $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $component = $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                    <!--[if BLOCK]><![endif]--><?php if(in_array('modeler', $professions ?? [])): ?>
                        <div class="card bg-base-300/50 p-4 mb-6 animate-fade-in">
                            <h3 class="font-medium mb-3"><?php echo e(__('application.modeler')); ?>:</h3>
                            <div class="grid grid-cols-1 gap-4">
                                <div>
                                    <label for="design_program" class="block text-sm font-medium"><?php echo e(__('application.3D Model Software')); ?></label>
                                    <?php if (isset($component)) { $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-input','data' => ['type' => 'text','placeholder' => ''.e(__('application.daw_placeholder')).'','variant' => 'outlined','id' => 'daw','wire:model.live' => 'daw']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-input'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'text','placeholder' => ''.e(__('application.daw_placeholder')).'','variant' => 'outlined','id' => 'daw','wire:model.live' => 'daw']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $attributes = $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $component = $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                    <!--[if BLOCK]><![endif]--><?php if(in_array('music_producer', $professions ?? [])): ?>
                        <div class="card bg-base-300/50 p-4 mb-6 animate-fade-in">
                            <h3 class="font-medium mb-3"><?php echo e(__('application.music_producer')); ?>:</h3>
                            <div class="grid grid-cols-1 gap-4">
                                <div>
                                    <label for="daw" class="block text-sm font-medium"><?php echo e(__('application.daw')); ?></label>
                                    <?php if (isset($component)) { $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-input','data' => ['type' => 'text','placeholder' => ''.e(__('application.daw_placeholder')).'','variant' => 'outlined','id' => 'daw','wire:model.live' => 'daw']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-input'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'text','placeholder' => ''.e(__('application.daw_placeholder')).'','variant' => 'outlined','id' => 'daw','wire:model.live' => 'daw']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $attributes = $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $component = $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                    <!--[if BLOCK]><![endif]--><?php if(in_array('other', $professions ?? [])): ?>
                        <div class="card bg-base-300/50 p-4 mb-6 animate-fade-in">
                            <h3 class="font-medium mb-3"><?php echo e(__('application.other_profession')); ?>:</h3>
                            <div class="grid grid-cols-1 gap-4">
                                <div>
                                    <label for="otherProfession" class="block text-sm font-medium"><?php echo e(__('application.other_profession')); ?></label>
                                    <?php if (isset($component)) { $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modern-input','data' => ['type' => 'text','placeholder' => ''.e(__('application.other_profession_placeholder')).'','variant' => 'outlined','id' => 'otherProfession','wire:model.live' => 'otherProfession']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modern-input'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'text','placeholder' => ''.e(__('application.other_profession_placeholder')).'','variant' => 'outlined','id' => 'otherProfession','wire:model.live' => 'otherProfession']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $attributes = $__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__attributesOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5)): ?>
<?php $component = $__componentOriginal17d23b099a28c4e2c5f6c448deb256e5; ?>
<?php unset($__componentOriginal17d23b099a28c4e2c5f6c448deb256e5); ?>
<?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                    <div>
                        <label for="portfolio" class="block text-sm font-medium"><?php echo e(__('application.portfolio')); ?></label>
                        <textarea id="portfolio" wire:model.live="portfolio" rows="3" class="mt-1 textarea textarea-bordered w-full" placeholder="<?php echo e(__('application.portfolio_placeholder')); ?>"></textarea>
                    </div>
                    <div>
                        <label for="desired_role" class="block text-sm font-medium"><?php echo e(__('application.desired_role')); ?></label>
                        <textarea id="desired_role" wire:model.live="desired_role" rows="3" class="mt-1 textarea textarea-bordered w-full" placeholder="<?php echo e(__('application.desired_role_placeholder')); ?>"></textarea>
                    </div>
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

            <!-- Step 3: About You -->
            <!--[if BLOCK]><![endif]--><?php if($currentStep == 3): ?>
                <div class="space-y-6 animate-slide-up">
                    <h2 class="text-xl font-semibold mb-4"><?php echo e(__('application.about_you')); ?></h2>

                    <div>
                        <label for="about_you" class="block text-sm font-medium"><?php echo e(__('application.about_you')); ?></label>
                        <textarea id="about_you" wire:model.live="about_you" rows="5" class="mt-1 textarea textarea-bordered w-full" placeholder="<?php echo e(__('application.about_you_placeholder')); ?>"></textarea>
                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['about_you'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-error text-sm"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                        <div class="text-xs text-right mt-1"><?php echo e(strlen($about_you ?? '')); ?>/50+ <?php echo e(__('application.characters')); ?></div>
                    </div>

                    <div>
                        <label for="strengths_weaknesses" class="block text-sm font-medium"><?php echo e(__('application.strengths_weaknesses')); ?></label>
                        <textarea id="strengths_weaknesses" wire:model.live="strengths_weaknesses" rows="5" class="mt-1 textarea textarea-bordered w-full" placeholder="<?php echo e(__('application.strengths_weaknesses_placeholder')); ?>"></textarea>
                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['strengths_weaknesses'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-error text-sm"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                        <div class="text-xs text-right mt-1"><?php echo e(strlen($strengths_weaknesses ?? '')); ?>/50+ <?php echo e(__('application.characters')); ?></div>
                    </div>

                    <div>
                        <label for="final_words" class="block text-sm font-medium"><?php echo e(__('application.final_words')); ?> (<?php echo e(__('application.optional')); ?>)</label>
                        <textarea id="final_words" wire:model.live="final_words" rows="3" class="mt-1 textarea textarea-bordered w-full" placeholder="<?php echo e(__('application.final_words_placeholder')); ?>"></textarea>
                    </div>
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

            <!-- Step 4: Review -->
            <!--[if BLOCK]><![endif]--><?php if($currentStep == 4): ?>
                <div class="space-y-6 animate-slide-up">
                    <h2 class="text-xl font-semibold mb-4"><?php echo e(__('application.review_submit')); ?></h2>

                    <div class="space-y-4">
                        <!-- Personal Information Review -->
                        <div class="collapse collapse-arrow bg-base-300/50">
                            <input type="checkbox" checked />
                            <div class="collapse-title font-medium">
                                <?php echo e(__('application.personal_data')); ?>

                            </div>
                            <div class="collapse-content">
                                <dl class="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-2 text-sm">
                                    <div>
                                        <dt class="font-medium"><?php echo e(__('application.name')); ?>:</dt>
                                        <dd><?php echo e($name); ?></dd>
                                    </div>
                                    <div>
                                        <dt class="font-medium"><?php echo e(__('application.age')); ?>:</dt>
                                        <dd><?php echo e($age); ?></dd>
                                    </div>
                                    <div>
                                        <dt class="font-medium"><?php echo e(__('application.gender')); ?>:</dt>
                                        <dd><?php echo e(match($gender) {
                                        'male' => __('application.gender_male'),
                                        'female' => __('application.gender_female'),
                                        'diverse' => __('application.gender_diverse'),
                                        'prefer_not_to_say' => __('application.gender_no_info'),
                                        default => $gender
                                    }); ?></dd>
                                    </div>
                                    <div>
                                        <dt class="font-medium"><?php echo e(__('application.pronouns')); ?>:</dt>
                                        <dd><?php echo e($pronouns ?: __('application.not_specified')); ?></dd>
                                    </div>
                                    <div class="sm:col-span-2">
                                        <dt class="font-medium"><?php echo e(__('application.professions')); ?>:</dt>
                                        <dd>
                                            <div class="flex flex-wrap gap-1 mt-1">
                                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $professions ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $profession): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <!--[if BLOCK]><![endif]--><?php if($profession !== 'other'): ?>
                                                        <span class="badge badge-primary"><?php echo e($professionOptions[$profession] ?? $profession); ?></span>
                                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

                                                <?php if(in_array('other', $professions ?? []) && $otherProfession): ?>
                                                    <span class="badge badge-secondary"><?php echo e($otherProfession); ?></span>
                                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                            </div>
                                        </dd>
                                    </div>
                                </dl>
                            </div>
                        </div>


                        <!-- Role-specific Information Review -->
                        <div class="collapse collapse-arrow bg-base-300/50">
                            <input type="checkbox" checked />
                            <div class="collapse-title font-medium">
                                <?php echo e(__('application.role_specific')); ?>

                            </div>
                            <div class="collapse-content">
                                <dl class="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-2 text-sm">
                                    <!--[if BLOCK]><![endif]--><?php if(!empty($checkboxQuestions)): ?>
                                        <div class="collapse collapse-arrow bg-base-300/50">
                                            <input type="checkbox" checked />
                                            <div class="collapse-title font-medium">
                                                <?php echo e(__('application.checkbox_questions')); ?>

                                            </div>
                                            <div class="collapse-content">
                                                <ul class="list-disc list-inside">
                                                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $checkboxQuestions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $question => $answer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <li>
                                                            <strong><?php echo e($question); ?>:</strong>
                                                            <?php echo e($answer ? __('application.yes') : __('application.no')); ?>

                                                        </li>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                                </ul>
                                            </div>
                                        </div>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                    <!--[if BLOCK]><![endif]--><?php if($voice_type): ?>
                                        <div>
                                            <dt class="font-medium"><?php echo e(__('application.voice_type')); ?>:</dt>
                                            <dd><?php echo e(match($voice_type) {
                                        'deep' => __('application.voice_type_deep'),
                                        'medium' => __('application.voice_type_medium'),
                                        'high' => __('application.voice_type_high'),
                                        default => $voice_type
                                    }); ?></dd>
                                        </div>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                    <!--[if BLOCK]><![endif]--><?php if($microphone): ?>
                                        <div>
                                            <dt class="font-medium"><?php echo e(__('application.microphone')); ?>:</dt>
                                            <dd><?php echo e($microphone); ?></dd>
                                        </div>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                    <!--[if BLOCK]><![endif]--><?php if($ram): ?>
                                        <div>
                                            <dt class="font-medium"><?php echo e(__('application.ram')); ?>:</dt>
                                            <dd><?php echo e($ram); ?></dd>
                                        </div>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                    <!--[if BLOCK]><![endif]--><?php if($fps): ?>
                                        <div>
                                            <dt class="font-medium"><?php echo e(__('application.fps')); ?>:</dt>
                                            <dd><?php echo e($fps); ?></dd>
                                        </div>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                    <!--[if BLOCK]><![endif]--><?php if($gpu): ?>
                                        <div>
                                            <dt class="font-medium"><?php echo e(__('application.gpu')); ?>:</dt>
                                            <dd><?php echo e($gpu); ?></dd>
                                        </div>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                    <!--[if BLOCK]><![endif]--><?php if($program): ?>
                                        <div>
                                            <dt class="font-medium"><?php echo e(__('application.design_programs')); ?>:</dt>
                                            <dd><?php echo e($program); ?></dd>
                                        </div>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                    <!--[if BLOCK]><![endif]--><?php if($design_style): ?>
                                        <div>
                                            <dt class="font-medium"><?php echo e(__('application.design_style')); ?>:</dt>
                                            <dd><?php echo e($design_style); ?></dd>
                                        </div>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                    <!--[if BLOCK]><![endif]--><?php if($favorite_design): ?>
                                        <div>
                                            <dt class="font-medium"><?php echo e(__('application.favorite_design')); ?>:</dt>
                                            <dd><?php echo e($favorite_design); ?></dd>
                                        </div>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                    <!--[if BLOCK]><![endif]--><?php if($portfolio): ?>
                                        <div>
                                            <dt class="font-medium"><?php echo e(__('application.portfolio')); ?>:</dt>
                                            <dd><a href="<?php echo e($portfolio); ?>" class="link link-primary" target="_blank"><?php echo e($portfolio); ?></a></dd>
                                        </div>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                    <!--[if BLOCK]><![endif]--><?php if($languages): ?>
                                        <div>
                                            <dt class="font-medium"><?php echo e(__('application.programming_languages')); ?>:</dt>
                                            <dd><?php echo e($languages); ?></dd>
                                        </div>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                    <!--[if BLOCK]><![endif]--><?php if($ide): ?>
                                        <div>
                                            <dt class="font-medium"><?php echo e(__('application.preferred_ide')); ?>:</dt>
                                            <dd><?php echo e($ide); ?></dd>
                                        </div>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                    <!--[if BLOCK]><![endif]--><?php if($daw): ?>
                                        <div>
                                            <dt class="font-medium"><?php echo e(__('application.daw')); ?>:</dt>
                                            <dd><?php echo e($daw); ?></dd>
                                        </div>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                    <!--[if BLOCK]><![endif]--><?php if($desired_role): ?>
                                        <div class="sm:col-span-2">
                                            <dt class="font-medium"><?php echo e(__('application.desired_role')); ?>:</dt>
                                            <dd class="whitespace-pre-line"><?php echo e($desired_role); ?></dd>
                                        </div>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                </dl>
                            </div>
                        </div>

                        <!-- About You Review -->
                        <div class="collapse collapse-arrow bg-base-300/50">
                            <input type="checkbox" checked />
                            <div class="collapse-title font-medium">
                                <?php echo e(__('application.about_you')); ?>

                            </div>
                            <div class="collapse-content">
                                <dl class="grid grid-cols-1 gap-y-4 text-sm">
                                    <div>
                                        <dt class="font-medium"><?php echo e(__('application.about_you')); ?>:</dt>
                                        <dd class="mt-1 whitespace-pre-line"><?php echo e($about_you); ?></dd>
                                    </div>

                                    <div>
                                        <dt class="font-medium"><?php echo e(__('application.strengths_weaknesses')); ?>:</dt>
                                        <dd class="mt-1 whitespace-pre-line"><?php echo e($strengths_weaknesses); ?></dd>
                                    </div>

                                    <!--[if BLOCK]><![endif]--><?php if($final_words): ?>
                                        <div>
                                            <dt class="font-medium"><?php echo e(__('application.final_words')); ?>:</dt>
                                            <dd class="mt-1 whitespace-pre-line"><?php echo e($final_words); ?></dd>
                                        </div>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                </dl>
                            </div>
                        </div>

                        <!-- Confirmation -->
                        <div class="mt-6 flex items-start">
                            <div class="flex h-5 items-center">
                                <input id="confirmation" wire:model.live="confirmation" type="checkbox" class="checkbox checkbox-primary">
                            </div>
                            <div class="ml-3 text-sm">
                                <label for="confirmation" class="font-medium">
                                    <?php echo e(__('application.confirm_correct_information')); ?>

                                </label>
                                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['confirmation'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-error text-sm block"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </div>

        </div>
    </div>

    <!-- Modern Navigation Footer -->
    <div class="fixed bottom-0 left-0 right-0 bg-base-100/95 backdrop-blur-lg border-t border-base-300/50 z-50">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex <?php if($currentStep > 1): ?> justify-between <?php else: ?> justify-end <?php endif; ?> items-center">
                <!--[if BLOCK]><![endif]--><?php if($currentStep > 1): ?>
                    <button type="button" wire:click="previousStep"
                            class="flex items-center gap-3 px-6 py-3 text-base-content/70 hover:text-base-content transition-colors duration-200 group">
                        <div class="w-10 h-10 rounded-full bg-base-200 group-hover:bg-base-300 flex items-center justify-center transition-colors duration-200">
                            <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-arrow-left'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'h-5 w-5']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                        </div>
                        <span class="font-medium"><?php echo e(__('application.previous_step')); ?></span>
                    </button>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                <div class="flex items-center gap-4">
                    <!--[if BLOCK]><![endif]--><?php if($currentStep < 4): ?>
                        <button type="button" wire:click="nextStep"
                                class="flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-primary to-secondary text-white rounded-2xl font-semibold text-lg hover:shadow-lg hover:scale-105 transition-all duration-200 group">
                            <span><?php echo e(__('application.next_step')); ?></span>
                            <div class="w-6 h-6 rounded-full bg-white/20 group-hover:bg-white/30 flex items-center justify-center transition-colors duration-200">
                                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-arrow-right'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'h-4 w-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                            </div>
                        </button>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                    <!--[if BLOCK]><![endif]--><?php if($currentStep == 4): ?>
                        <button type="button" wire:click="submitApplication"
                                class="flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-success to-emerald-600 text-white rounded-2xl font-semibold text-lg hover:shadow-lg hover:scale-105 transition-all duration-200 group">
                            <span><?php echo e(__('application.submit_application')); ?></span>
                            <div class="w-6 h-6 rounded-full bg-white/20 group-hover:bg-white/30 flex items-center justify-center transition-colors duration-200">
                                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-check-circle'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'h-4 w-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                            </div>
                        </button>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom padding to account for fixed footer -->
    <div class="h-24"></div>

    <!-- Loading indicator -->
    <div wire:loading wire:target="nextStep, previousStep, submitApplication" class="fixed inset-0 flex items-center justify-center bg-base-100/75 z-50">
        <div class="text-center">
            <div class="w-16 h-16 border-4 border-primary/30 border-t-primary rounded-full animate-spin mx-auto mb-4"></div>
            <p class="text-lg font-medium text-base-content"><?php echo e(__('application.processing')); ?></p>
        </div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\minewache-website\resources\views/livewire/application-wizard.blade.php ENDPATH**/ ?>