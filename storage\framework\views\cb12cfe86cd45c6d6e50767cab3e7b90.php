<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'type' => 'text',
    'label' => null,
    'placeholder' => null,
    'error' => null,
    'helper' => null,
    'icon' => null,
    'iconPosition' => 'left',
    'size' => 'md',
    'variant' => 'default',
    'required' => false,
    'disabled' => false
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'type' => 'text',
    'label' => null,
    'placeholder' => null,
    'error' => null,
    'helper' => null,
    'icon' => null,
    'iconPosition' => 'left',
    'size' => 'md',
    'variant' => 'default',
    'required' => false,
    'disabled' => false
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
$id = $attributes->get('id', 'input-' . uniqid());

$baseClasses = 'w-full transition-all duration-300 ease-out focus:outline-none focus:ring-2 focus:ring-offset-1 disabled:opacity-50 disabled:cursor-not-allowed';

$variantClasses = match($variant) {
    'filled' => 'bg-base-200 border-0 focus:bg-base-100 focus:ring-primary',
    'outlined' => 'bg-transparent border-2 border-base-300 focus:border-primary focus:ring-primary',
    'underlined' => 'bg-transparent border-0 border-b-2 border-base-300 focus:border-primary focus:ring-0 rounded-none',
    'glass' => 'glass border border-base-300/20 focus:border-primary/50 focus:ring-primary/30',
    default => 'bg-base-100 border border-base-300 focus:border-primary focus:ring-primary'
};

$sizeClasses = match($size) {
    'sm' => 'px-3 py-2 text-sm rounded-lg',
    'md' => 'px-4 py-3 text-base rounded-xl',
    'lg' => 'px-5 py-4 text-lg rounded-xl',
    default => 'px-4 py-3 text-base rounded-xl'
};

$errorClasses = $error ? 'border-error focus:border-error focus:ring-error' : '';

$iconPadding = $icon ? ($iconPosition === 'left' ? 'pl-12' : 'pr-12') : '';

$classes = trim("{$baseClasses} {$variantClasses} {$sizeClasses} {$errorClasses} {$iconPadding}");
?>

<div class="space-y-2">
    <!--[if BLOCK]><![endif]--><?php if($label): ?>
        <label for="<?php echo e($id); ?>" class="block text-sm font-medium text-base-content">
            <?php echo e($label); ?>

            <!--[if BLOCK]><![endif]--><?php if($required): ?>
                <span class="text-error ml-1">*</span>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </label>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    
    <div class="relative">
        <!--[if BLOCK]><![endif]--><?php if($icon): ?>
            <div class="absolute inset-y-0 <?php echo e($iconPosition === 'left' ? 'left-0 pl-3' : 'right-0 pr-3'); ?> flex items-center pointer-events-none">
                <div class="h-5 w-5 text-base-content/60">
                    <?php echo $icon; ?>

                </div>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        
        <input
            type="<?php echo e($type); ?>"
            id="<?php echo e($id); ?>"
            <?php if($placeholder): ?> placeholder="<?php echo e($placeholder); ?>" <?php endif; ?>
            <?php if($required): ?> required <?php endif; ?>
            <?php if($disabled): ?> disabled <?php endif; ?>
            <?php echo e($attributes->merge(['class' => $classes])); ?>

        />
        
        <!--[if BLOCK]><![endif]--><?php if($error): ?>
            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-error" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>
    
    <!--[if BLOCK]><![endif]--><?php if($error): ?>
        <p class="text-sm text-error flex items-center gap-1">
            <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
            <?php echo e($error); ?>

        </p>
    <?php elseif($helper): ?>
        <p class="text-sm text-base-content/60"><?php echo e($helper); ?></p>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
</div>

<style>
    /* Enhanced input focus effects */
    .input-focus-glow:focus {
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 0 20px rgba(59, 130, 246, 0.2);
    }
    
    /* Floating label effect */
    .input-floating {
        position: relative;
    }
    
    .input-floating input:focus + label,
    .input-floating input:not(:placeholder-shown) + label {
        transform: translateY(-1.5rem) scale(0.875);
        color: rgb(59 130 246);
    }
    
    .input-floating label {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        transition: all 0.2s ease-out;
        pointer-events: none;
        background: rgb(var(--color-base-100));
        padding: 0 0.25rem;
    }
    
    /* Glass input styling */
    .input-glass {
        backdrop-filter: blur(12px);
        background: rgba(var(--color-base-100-rgb), 0.7);
    }
    
    /* High contrast theme adjustments */
    [data-theme="minewache-high-contrast"] input {
        border-width: 2px !important;
        font-weight: 500;
    }
    
    [data-theme="minewache-high-contrast"] input:focus {
        border-color: #0066ff !important;
        box-shadow: 0 0 0 3px rgba(0, 102, 255, 0.3) !important;
    }
    
    /* Colorful theme input styles */
    [data-theme="minewache-colorful"] input:focus {
        border-image: linear-gradient(135deg, #8b5cf6, #06b6d4, #f59e0b) 1;
        box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
    }
    
    /* Smooth placeholder transitions */
    input::placeholder {
        transition: opacity 0.3s ease;
    }
    
    input:focus::placeholder {
        opacity: 0.5;
    }
    
    /* Error state animations */
    .input-error {
        animation: shake 0.5s ease-in-out;
    }
    
    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
    }
    
    /* Reduced motion support */
    @media (prefers-reduced-motion: reduce) {
        .input-error {
            animation: none;
        }
        
        .input-floating label {
            transition: none;
        }
    }
</style>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\minewache-website\resources\views/components/modern-input.blade.php ENDPATH**/ ?>